'use client';

import { useState } from 'react';
import VideoUpload from './components/VideoUpload';
import JobStatus from './components/JobStatus';

export default function Home() {
  const [currentJobId, setCurrentJobId] = useState<string | null>(null);

  const handleUploadComplete = (jobId: string) => {
    setCurrentJobId(jobId);
  };

  const handleReset = () => {
    setCurrentJobId(null);
  };

  return (
    <div className="space-y-8">
      {!currentJobId ? (
        <VideoUpload onUploadComplete={handleUploadComplete} />
      ) : (
        <JobStatus jobId={currentJobId} onReset={handleReset} />
      )}

      {/* App Description */}
      <div className="max-w-4xl mx-auto bg-white/10 backdrop-blur-sm rounded-lg shadow-lg p-8 mb-8">
        <div className="text-center">
          <p className="text-lg text-white leading-relaxed mb-6">
            M<PERSON>lips instantly turns your Mobile Legends matches into bite-sized highlight reels.
            Just upload any match recording and M<PERSON><PERSON><PERSON> automatically pulls out First Bloods,
            multi-kill streaks (Double Kill → Triple Kill → Mega Kill → Maniac → Savage),
            Godlike runs, and more—then packages them into polished 10–15 second clips ready for
            TikTok, Instagram Reels, YouTube Shorts, or wherever you share your greatest plays.
          </p>
          <p className="text-xl text-purple-300 font-semibold">
            No editing required—focus on the game, and let MLClips handle the rest.
          </p>
        </div>
      </div>

      {/* How it works */}
      <div className="max-w-4xl mx-auto bg-white/95 backdrop-blur-sm rounded-lg shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
          How MLClips Works
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="text-4xl mb-4">📱</div>
            <h3 className="font-bold text-gray-900 mb-3">1. Upload Match Recording</h3>
            <p className="text-gray-600">
              Drag and drop your Mobile Legends match recording or click to browse files
            </p>
          </div>
          <div className="text-center">
            <div className="text-4xl mb-4">🤖</div>
            <h3 className="font-bold text-gray-900 mb-3">2. AI Highlight Detection</h3>
            <p className="text-gray-600">
              Our slam-1 AI model analyzes the audio to automatically detect gaming moments and achievements
            </p>
          </div>
          <div className="text-center">
            <div className="text-4xl mb-4">🎬</div>
            <h3 className="font-bold text-gray-900 mb-3">3. Share Your Clips</h3>
            <p className="text-gray-600">
              Get polished 10-15 second clips ready for TikTok, Instagram Reels, and YouTube Shorts
            </p>
          </div>
        </div>

        <div className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
          <h4 className="font-bold text-blue-900 mb-4 text-lg">🎯 Mobile Legends Moments We Detect:</h4>
          <div className="space-y-4">
            <div>
              <h5 className="text-sm font-bold text-red-800 mb-2 flex items-center">
                <span className="mr-2">⚔️</span>
                Multi-Kill Sequences:
              </h5>
              <div className="flex flex-wrap gap-2">
                {['Double Kill', 'Triple Kill', 'Mega Kill', 'Maniac', 'Savage'].map((trigger) => (
                  <span
                    key={trigger}
                    className="px-4 py-2 bg-red-100 text-red-800 rounded-full text-sm font-bold border border-red-200"
                  >
                    {trigger}
                  </span>
                ))}
              </div>
            </div>
            <div>
              <h5 className="text-sm font-bold text-blue-800 mb-2 flex items-center">
                <span className="mr-2">🏆</span>
                Epic Achievements:
              </h5>
              <div className="flex flex-wrap gap-2">
                {['First Blood', 'Godlike', 'Killing Spree', 'Legendary', 'Monster Kill', 'Unstoppable'].map((trigger) => (
                  <span
                    key={trigger}
                    className="px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium border border-blue-200"
                  >
                    {trigger}
                  </span>
                ))}
              </div>
            </div>
          </div>
          <div className="mt-4 p-3 bg-white/70 rounded-lg">
            <p className="text-sm text-gray-700">
              <strong className="text-purple-800">🧠 Smart Detection:</strong> Our slam-1 AI model captures complete sequences
              (e.g., "You have slain an enemy" → "Double Kill" → "Triple Kill") and understands Mobile Legends terminology
              for maximum accuracy.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
