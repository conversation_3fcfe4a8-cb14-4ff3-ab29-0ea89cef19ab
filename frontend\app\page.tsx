'use client';

import { useState } from 'react';
import VideoUpload from './components/VideoUpload';
import JobStatus from './components/JobStatus';

export default function Home() {
  const [currentJobId, setCurrentJobId] = useState<string | null>(null);

  const handleUploadComplete = (jobId: string) => {
    setCurrentJobId(jobId);
  };

  const handleReset = () => {
    setCurrentJobId(null);
  };

  return (
    <div className="space-y-8">
      {!currentJobId ? (
        <VideoUpload onUploadComplete={handleUploadComplete} />
      ) : (
        <JobStatus jobId={currentJobId} onReset={handleReset} />
      )}

      {/* Instructions */}
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          How it works
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-3xl mb-3">📁</div>
            <h3 className="font-semibold text-gray-900 mb-2">1. Upload Video</h3>
            <p className="text-gray-600 text-sm">
              Drag and drop your gaming video or click to browse files
            </p>
          </div>
          <div className="text-center">
            <div className="text-3xl mb-3">🎯</div>
            <h3 className="font-semibold text-gray-900 mb-2">2. AI Processing</h3>
            <p className="text-gray-600 text-sm">
              Our AI analyzes the audio to find gaming highlights like kills and achievements
            </p>
          </div>
          <div className="text-center">
            <div className="text-3xl mb-3">🎬</div>
            <h3 className="font-semibold text-gray-900 mb-2">3. Get Clips</h3>
            <p className="text-gray-600 text-sm">
              Download individual highlight clips ready for sharing
            </p>
          </div>
        </div>
        
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-semibold text-blue-900 mb-2">Supported Triggers:</h4>
          <div className="space-y-3">
            <div>
              <h5 className="text-sm font-medium text-blue-800 mb-1">Multi-Kill Sequences:</h5>
              <div className="flex flex-wrap gap-2">
                {['Double Kill', 'Triple Kill', 'Mega Kill', 'Maniac', 'Savage'].map((trigger) => (
                  <span
                    key={trigger}
                    className="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium"
                  >
                    {trigger}
                  </span>
                ))}
              </div>
            </div>
            <div>
              <h5 className="text-sm font-medium text-blue-800 mb-1">Individual Achievements:</h5>
              <div className="flex flex-wrap gap-2">
                {['First Blood', 'Godlike', 'Killing Spree', 'Legendary', 'Monster Kill', 'Unstoppable'].map((trigger) => (
                  <span
                    key={trigger}
                    className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                  >
                    {trigger}
                  </span>
                ))}
              </div>
            </div>
          </div>
          <div className="mt-3 text-xs text-blue-700">
            <p><strong>Smart Detection:</strong> Multi-kill sequences automatically capture the full progression (e.g., "You have slain an enemy" → "Double Kill" → "Triple Kill")</p>
          </div>
        </div>
      </div>
    </div>
  );
}
