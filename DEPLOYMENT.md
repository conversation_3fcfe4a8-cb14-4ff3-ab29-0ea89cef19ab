# Deployment Guide

This guide covers deploying the Video Highlight Extractor to various cloud platforms.

## 🚀 Quick Deploy Options

### Option 1: Vercel (Frontend) + Fly.io (Backend)
**Recommended for production**

#### Deploy Backend to Fly.io

1. **Install Fly CLI**
   ```bash
   curl -L https://fly.io/install.sh | sh
   ```

2. **Login and Initialize**
   ```bash
   fly auth login
   cd backend
   fly launch --no-deploy
   ```

3. **Set Environment Variables**
   ```bash
   fly secrets set HF_ENDPOINT="https://your-endpoint.hf.space"
   fly secrets set HF_TOKEN="hf_your_token_here"
   fly secrets set CORS_ORIGINS="https://your-frontend-domain.vercel.app"
   ```

4. **Create Volumes for Persistent Storage**
   ```bash
   fly volumes create video_storage --size 10
   fly volumes create clips_storage --size 20
   fly volumes create jobs_storage --size 1
   ```

5. **Deploy**
   ```bash
   fly deploy
   ```

#### Deploy Frontend to Vercel

1. **Connect GitHub Repository**
   - Go to [vercel.com](https://vercel.com)
   - Import your GitHub repository
   - Set root directory to `frontend`

2. **Configure Environment Variables**
   ```
   NEXT_PUBLIC_API_URL=https://your-app.fly.dev
   ```

3. **Deploy**
   - Vercel will automatically deploy on push to main branch

### Option 2: Render (Full Stack)

1. **Create Web Service for Backend**
   - Connect your GitHub repository
   - Set build command: `cd backend && pip install -r requirements.txt`
   - Set start command: `cd backend && uvicorn main:app --host 0.0.0.0 --port $PORT`
   - Add environment variables:
     - `HF_ENDPOINT`
     - `HF_TOKEN`
     - `CORS_ORIGINS`

2. **Create Static Site for Frontend**
   - Connect your GitHub repository
   - Set build command: `cd frontend && npm install && npm run build`
   - Set publish directory: `frontend/out`
   - Add environment variable:
     - `NEXT_PUBLIC_API_URL`

### Option 3: Railway

1. **Deploy Backend**
   ```bash
   railway login
   railway new
   railway add
   ```

2. **Set Environment Variables**
   ```bash
   railway variables set HF_ENDPOINT="https://your-endpoint.hf.space"
   railway variables set HF_TOKEN="hf_your_token_here"
   ```

3. **Deploy**
   ```bash
   railway up
   ```

## 🔧 Environment Variables

### Backend Required Variables
```bash
HF_ENDPOINT=https://your-endpoint.hf.space
HF_TOKEN=hf_your_token_here
CORS_ORIGINS=https://your-frontend-domain.com
```

### Frontend Required Variables
```bash
NEXT_PUBLIC_API_URL=https://your-backend-domain.com
```

## 📊 Monitoring and Logging

### Health Checks
- Backend health endpoint: `GET /health`
- Returns `{"status": "healthy"}` when operational

### Logging
- Backend logs to stdout (captured by platform)
- Frontend logs to browser console and Vercel analytics

### Metrics to Monitor
- API response times
- Video processing success rate
- Storage usage
- Error rates

## 🔒 Security Considerations

### API Security
- CORS properly configured
- File upload size limits enforced
- Input validation on all endpoints

### Storage Security
- Temporary file cleanup
- Access controls on static files
- Regular cleanup of old jobs

### Environment Security
- Never commit `.env` files
- Use platform secret management
- Rotate API tokens regularly

## 📈 Scaling Considerations

### Backend Scaling
- Stateless design allows horizontal scaling
- File storage should use persistent volumes
- Consider Redis for job queue in high-traffic scenarios

### Frontend Scaling
- Static site scales automatically
- CDN distribution via Vercel/Netlify
- API calls are cached where appropriate

### Storage Scaling
- Monitor disk usage
- Implement cleanup policies
- Consider object storage (S3, GCS) for large deployments

## 🐛 Troubleshooting

### Common Issues

1. **CORS Errors**
   - Check `CORS_ORIGINS` environment variable
   - Ensure frontend URL is included

2. **File Upload Failures**
   - Check disk space on backend
   - Verify file size limits
   - Check FFmpeg installation

3. **Processing Timeouts**
   - Increase platform timeout limits
   - Monitor HF API response times
   - Check video file sizes

4. **Static File 404s**
   - Verify static file mounting
   - Check file permissions
   - Ensure clips directory exists

### Debug Commands

```bash
# Check backend logs
fly logs -a your-app-name

# Check frontend logs
vercel logs your-deployment-url

# Test API endpoints
curl https://your-api-domain.com/health
curl https://your-api-domain.com/status/test-job-id

# Check environment variables
fly ssh console -a your-app-name
env | grep HF_
```

## 🔄 CI/CD Pipeline

The included GitHub Actions workflow automatically:
- Runs tests on pull requests
- Builds Docker images on main branch
- Can be extended for automatic deployment

### Extending for Auto-Deploy

Add deployment steps to `.github/workflows/ci.yml`:

```yaml
- name: Deploy to Fly.io
  if: github.ref == 'refs/heads/main'
  run: |
    fly deploy --remote-only
  env:
    FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
```

## 📋 Pre-Deployment Checklist

- [ ] Environment variables configured
- [ ] HF endpoint tested and working
- [ ] CORS origins properly set
- [ ] Storage volumes created (if needed)
- [ ] Health checks passing
- [ ] Error monitoring configured
- [ ] Backup strategy in place
- [ ] SSL certificates configured
- [ ] Domain names configured
- [ ] Performance testing completed

## 🆘 Support

For deployment issues:
1. Check platform-specific documentation
2. Review application logs
3. Test API endpoints manually
4. Verify environment configuration
5. Check resource limits and quotas
