# fly.toml app configuration file generated for video-highlight-extractor on 2024-01-01T00:00:00Z
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = "video-highlight-extractor"
primary_region = "ord"

[build]
  dockerfile = "backend/Dockerfile"

[env]
  PORT = "8000"

[http_service]
  internal_port = 8000
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0
  processes = ["app"]

[[http_service.checks]]
  grace_period = "10s"
  interval = "30s"
  method = "GET"
  timeout = "5s"
  path = "/health"

[mounts]
  source = "video_storage"
  destination = "/app/uploads"

[mounts]
  source = "clips_storage"
  destination = "/app/static"

[mounts]
  source = "jobs_storage"
  destination = "/app/jobs"
