#!/usr/bin/env python3
"""
Test the sample video to debug the single clip issue
"""

import asyncio
import sys
import os
import uuid
from pathlib import Path

sys.path.append('backend')

from backend.services.video_processor import VideoProcessor
from backend.services.job_manager import JobManager

async def test_sample_video():
    """Test processing the sample video"""
    print("🎮 Testing Sample Gaming Video")
    print("=" * 35)
    
    # Path to sample video
    sample_video = Path("backend/samples/24 Kills + MANIAC!! Alucard Best Build Exp Lane!! - Build Top 1 Global Alucard ~ MLBB.mp4")
    
    if not sample_video.exists():
        print(f"❌ Sample video not found: {sample_video}")
        return
    
    print(f"📹 Sample video: {sample_video.name}")
    print(f"📁 File size: {sample_video.stat().st_size / 1024 / 1024:.1f} MB")
    
    # Create job manager and video processor
    job_manager = JobManager()
    processor = VideoProcessor()
    
    # Generate test job ID
    job_id = str(uuid.uuid4())
    print(f"🆔 Job ID: {job_id}")
    
    # Create job
    job_manager.create_job(job_id)
    
    try:
        print("\n🚀 Starting video processing...")
        print("=" * 30)
        
        # Process the video
        await processor.process_video(job_id, str(sample_video), job_manager)
        
        # Check results
        status = job_manager.get_job_status(job_id)
        
        print(f"\n📊 Processing Results:")
        print(f"   Status: {status.status}")
        print(f"   Progress: {status.progress}%")
        print(f"   Clips created: {len(status.clips)}")
        
        if status.clips:
            print(f"\n🎯 Generated Clips:")
            for i, clip in enumerate(status.clips, 1):
                print(f"   {i:2d}. {clip}")
        else:
            print(f"\n❌ No clips were generated!")
            if hasattr(status, 'error') and status.error:
                print(f"   Error: {status.error}")
        
        return len(status.clips)
        
    except Exception as e:
        print(f"❌ Error processing video: {e}")
        import traceback
        traceback.print_exc()
        return 0

async def test_transcription_only():
    """Test just the transcription part to see what AssemblyAI returns"""
    print("\n🎤 Testing Transcription Only")
    print("=" * 30)
    
    sample_video = Path("backend/samples/24 Kills + MANIAC!! Alucard Best Build Exp Lane!! - Build Top 1 Global Alucard ~ MLBB.mp4")

    if not sample_video.exists():
        print(f"❌ Sample video not found: {sample_video}")
        # Try to find it in the current directory
        alt_path = Path("24 Kills + MANIAC!! Alucard Best Build Exp Lane!! - Build Top 1 Global Alucard ~ MLBB.mp4")
        if alt_path.exists():
            sample_video = alt_path
            print(f"✅ Found video in current directory")
        else:
            print(f"❌ Video not found anywhere")
            return
    
    processor = VideoProcessor()
    
    try:
        print("🔄 Transcribing video...")
        words = await processor._transcribe_video(str(sample_video))
        
        print(f"✅ Transcription complete!")
        print(f"📊 Total words: {len(words)}")
        
        if words:
            print(f"\n📝 First 20 words:")
            for i, word in enumerate(words[:20]):
                print(f"   {i+1:2d}. '{word.word}' ({word.start:.2f}s - {word.end:.2f}s)")
            
            print(f"\n🎮 Looking for gaming words...")
            gaming_words = ["kill", "blood", "enemy", "slain", "double", "triple", "mega", "first", "godlike", "legendary", "spree", "unstoppable", "monster", "maniac", "savage"]
            found_gaming = []
            
            for word in words:
                if any(gaming_word in word.word.lower() for gaming_word in gaming_words):
                    found_gaming.append(f"'{word.word}' at {word.start:.2f}s")
            
            if found_gaming:
                print(f"✅ Found {len(found_gaming)} gaming-related words:")
                for gaming_word in found_gaming[:10]:  # Show first 10
                    print(f"   • {gaming_word}")
                if len(found_gaming) > 10:
                    print(f"   ... and {len(found_gaming) - 10} more")
            else:
                print(f"❌ No gaming words detected in transcription")
                print(f"📝 Full transcription text:")
                full_text = " ".join([w.word for w in words])
                print(f"   {full_text[:200]}...")
        
        return words
        
    except Exception as e:
        print(f"❌ Transcription error: {e}")
        import traceback
        traceback.print_exc()
        return []

async def test_trigger_detection(words):
    """Test trigger detection on the transcribed words"""
    if not words:
        print("\n❌ No words to test trigger detection")
        return
    
    print("\n🎯 Testing Trigger Detection")
    print("=" * 30)
    
    processor = VideoProcessor()
    
    try:
        triggers = processor._find_triggers(words)
        
        print(f"✅ Trigger detection complete!")
        print(f"📊 Total triggers found: {len(triggers)}")
        
        if triggers:
            print(f"\n🎯 Detected Triggers:")
            for i, trigger in enumerate(triggers, 1):
                duration = trigger.end_time - trigger.start_time
                print(f"   {i:2d}. {trigger.trigger}")
                print(f"       ⏰ {trigger.timestamp:.2f}s")
                print(f"       📹 {trigger.start_time:.2f}s - {trigger.end_time:.2f}s ({duration:.1f}s)")
        else:
            print(f"❌ No triggers detected!")
        
        return triggers
        
    except Exception as e:
        print(f"❌ Trigger detection error: {e}")
        import traceback
        traceback.print_exc()
        return []

async def main():
    """Run all tests"""
    print("🚀 Sample Video Debug Test")
    print("=" * 30)
    
    # Test 1: Full processing
    clip_count = await test_sample_video()
    
    # Test 2: Just transcription
    words = await test_transcription_only()
    
    # Test 3: Just trigger detection
    triggers = await test_trigger_detection(words)
    
    print(f"\n🎉 Debug Test Complete!")
    print(f"=" * 25)
    print(f"📊 Results Summary:")
    print(f"   Words transcribed: {len(words) if words else 0}")
    print(f"   Triggers detected: {len(triggers) if triggers else 0}")
    print(f"   Clips created: {clip_count}")
    
    if clip_count == 1 and len(triggers) > 1:
        print(f"\n🔍 ISSUE IDENTIFIED:")
        print(f"   Multiple triggers detected but only 1 clip created!")
        print(f"   This suggests a problem in the clip creation process.")
    elif len(triggers) <= 1:
        print(f"\n🔍 ISSUE IDENTIFIED:")
        print(f"   Very few triggers detected from the video.")
        print(f"   This suggests a problem with transcription or trigger matching.")
    else:
        print(f"\n✅ Everything looks good!")

if __name__ == "__main__":
    asyncio.run(main())
