import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import ErrorBoundary from './components/ErrorBoundary'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'MLClips - Mobile Legends Highlight Extractor',
  description: 'Capture and share your best Mobile Legends moments in seconds. Automatically extract First Bloods, multi-kills, and epic plays from your match recordings.',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ErrorBoundary>
          <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900">
            <header className="bg-gradient-to-r from-blue-800 to-purple-800 shadow-lg">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div className="flex items-center space-x-3">
                  <div className="text-3xl">🎮</div>
                  <div>
                    <h1 className="text-3xl font-bold text-white">
                      MLClips
                    </h1>
                    <p className="text-blue-200 mt-1 font-medium">
                      Capture and share your best Mobile Legends moments in seconds.
                    </p>
                  </div>
                </div>
              </div>
            </header>
            <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
              {children}
            </main>
          </div>
        </ErrorBoundary>
      </body>
    </html>
  )
}
