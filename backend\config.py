import os
from typing import List
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # Hugging Face Configuration
    hf_endpoint: str = os.getenv("HF_ENDPOINT", "")
    hf_token: str = os.getenv("HF_TOKEN", "")
    
    # CORS Configuration
    cors_origins: List[str] = os.getenv("CORS_ORIGINS", "http://localhost:3000").split(",")
    
    # File paths
    upload_dir: str = "uploads"
    static_dir: str = "static"
    clips_dir: str = "static/clips"
    jobs_dir: str = "jobs"
    
    # Trigger words and their time ranges (seconds before and after)
    triggers: dict = {
        "double kill": [-3, 2],
        "triple kill": [-3, 2], 
        "maniac": [-4, 2],
        "savage": [-5, 3],
        "shutdown": [-2, 2]
    }
    
    # Processing settings
    merge_gap_threshold: float = 1.0  # seconds
    
    class Config:
        env_file = ".env"


settings = Settings()
