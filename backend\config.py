import os
from typing import List
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # Hugging Face Configuration
    hf_endpoint: str = os.getenv("HF_ENDPOINT", "")
    hf_token: str = os.getenv("HF_TOKEN", "")
    
    # CORS Configuration
    cors_origins: List[str] = os.getenv("CORS_ORIGINS", "http://localhost:3000").split(",")
    
    # File paths
    upload_dir: str = "uploads"
    static_dir: str = "static"
    clips_dir: str = "static/clips"
    jobs_dir: str = "jobs"
    
    # Trigger words and their time ranges (seconds before and after)
    triggers: dict = {
        "double kill": [-3, 2],
        "first blood": [-2, 2],
        "godlike": [-4, 3],
        "you have slain an enemy": [-1, 1],
        "killing spree": [-3, 2],
        "legendary": [-4, 3],
        "maniac": [-5, 3],
        "mega kill": [-4, 2],
        "monster kill": [-4, 2],
        "triple kill": [-4, 2],
        "unstoppable": [-4, 3],
        "savage": [-6, 3]
    }

    # Multi-kill sequence definitions
    multi_kill_sequences: dict = {
        "double kill": [
            ["first blood", "double kill"],
            ["you have slain an enemy", "double kill"]
        ],
        "triple kill": [
            ["you have slain an enemy", "double kill", "triple kill"]
        ],
        "mega kill": [
            ["you have slain an enemy", "double kill", "triple kill", "mega kill"]
        ],
        "maniac": [
            ["you have slain an enemy", "double kill", "triple kill", "mega kill", "maniac"]
        ],
        "savage": [
            ["you have slain an enemy", "double kill", "triple kill", "mega kill", "maniac", "savage"]
        ]
    }
    
    # Processing settings
    merge_gap_threshold: float = 1.0  # seconds
    
    class Config:
        env_file = ".env"


settings = Settings()
