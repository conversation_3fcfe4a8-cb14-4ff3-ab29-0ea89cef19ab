import os
import uuid
from pathlib import Path
from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, BackgroundTasks, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse

from config import settings
from models import UploadResponse, JobStatus
from services.video_processor import VideoProcessor
from services.job_manager import JobManager

# Create necessary directories
for directory in [settings.upload_dir, settings.static_dir, settings.clips_dir, settings.jobs_dir]:
    Path(directory).mkdir(parents=True, exist_ok=True)

app = FastAPI(
    title="Video Highlight Extractor API",
    description="Extract gaming highlights from videos using speech recognition",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Static files for serving video clips
app.mount("/clips", StaticFiles(directory=settings.clips_dir), name="clips")

# Initialize services
video_processor = VideoProcessor()
job_manager = JobManager()


@app.get("/")
async def root():
    return {"message": "Video Highlight Extractor API", "version": "1.0.0"}


@app.post("/upload", response_model=UploadResponse)
async def upload_video(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...)
):
    # Validate file type
    if not file.content_type or not file.content_type.startswith("video/"):
        raise HTTPException(status_code=400, detail="File must be a video")
    
    # Generate unique job ID
    job_id = str(uuid.uuid4())
    
    # Save uploaded file
    file_path = Path(settings.upload_dir) / f"{job_id}.mp4"
    
    try:
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to save file: {str(e)}")
    
    # Initialize job status
    job_manager.create_job(job_id)
    
    # Start background processing
    background_tasks.add_task(
        video_processor.process_video,
        job_id,
        str(file_path),
        job_manager
    )
    
    return UploadResponse(
        job_id=job_id,
        message="Video uploaded successfully. Processing started."
    )


@app.get("/status/{job_id}", response_model=JobStatus)
async def get_job_status(job_id: str):
    status = job_manager.get_job_status(job_id)
    if not status:
        raise HTTPException(status_code=404, detail="Job not found")
    return status


@app.get("/health")
async def health_check():
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
