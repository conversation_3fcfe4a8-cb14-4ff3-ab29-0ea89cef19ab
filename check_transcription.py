#!/usr/bin/env python3
"""
Check what AssemblyAI is actually transcribing from the sample video
"""

import asyncio
import sys
import os
from pathlib import Path

sys.path.append('backend')

from backend.services.video_processor import VideoProcessor

async def check_sample_transcription():
    """Check what's being transcribed from the sample video"""
    print("🎤 Checking Sample Video Transcription")
    print("=" * 40)
    
    sample_video = Path("backend/samples/24 Kills + MANIAC!! Alucard Best Build Exp Lane!! - Build Top 1 Global Alucard ~ MLBB.mp4")
    
    if not sample_video.exists():
        print(f"❌ Sample video not found: {sample_video}")
        return
    
    print(f"📹 Video: {sample_video.name}")
    
    processor = VideoProcessor()
    
    if not processor.client:
        print("❌ AssemblyAI client not configured")
        return
    
    try:
        print("🔄 Transcribing video (this may take a few minutes)...")
        words = await processor._transcribe_video(str(sample_video))
        
        print(f"\n✅ Transcription Complete!")
        print(f"📊 Total words transcribed: {len(words)}")
        
        if not words:
            print("❌ No words were transcribed!")
            return
        
        # Show full transcription
        full_text = " ".join([w.word for w in words])
        print(f"\n📝 Full Transcription Text:")
        print(f"   {full_text}")
        
        # Show word-by-word with timestamps
        print(f"\n📋 Word-by-Word Breakdown:")
        for i, word in enumerate(words):
            print(f"   {i+1:3d}. '{word.word}' ({word.start:.2f}s - {word.end:.2f}s)")
        
        # Look for gaming-related words
        gaming_keywords = [
            "kill", "kills", "double", "triple", "mega", "maniac", "savage",
            "first", "blood", "enemy", "enemies", "slain", "slayed", "eliminated",
            "spree", "streak", "legendary", "legend", "godlike", "god", "divine",
            "unstoppable", "monster", "rampage", "dominating", "beyond", "wicked",
            "alucard", "hero", "champion", "victory", "defeat"
        ]
        
        found_gaming = []
        for word in words:
            word_lower = word.word.lower().strip()
            if word_lower in gaming_keywords:
                found_gaming.append((word_lower, word.start))
        
        print(f"\n🎮 Gaming-Related Words Found:")
        if found_gaming:
            for word, timestamp in found_gaming:
                print(f"   • '{word}' at {timestamp:.2f}s")
        else:
            print("   ❌ No gaming keywords detected!")
        
        # Check for exact trigger phrases
        print(f"\n🎯 Checking for Exact Gaming Triggers:")
        triggers_to_check = [
            "first blood", "double kill", "triple kill", "mega kill", "maniac", "savage",
            "godlike", "legendary", "killing spree", "unstoppable", "monster kill",
            "you have slain an enemy"
        ]
        
        found_triggers = []
        for trigger in triggers_to_check:
            if trigger in full_text.lower():
                found_triggers.append(trigger)
                print(f"   ✅ Found: '{trigger}'")
            else:
                print(f"   ❌ Missing: '{trigger}'")
        
        print(f"\n📊 Summary:")
        print(f"   Total words: {len(words)}")
        print(f"   Gaming words: {len(found_gaming)}")
        print(f"   Exact triggers: {len(found_triggers)}")
        
        if len(found_triggers) <= 1:
            print(f"\n💡 Analysis:")
            print(f"   The video likely contains:")
            print(f"   1. Background music/sound effects (not speech)")
            print(f"   2. Non-English gaming announcements")
            print(f"   3. Gaming sounds that aren't clear speech")
            print(f"   4. Or the title is misleading about content")
        
        return words, found_triggers
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return [], []

async def main():
    """Main function"""
    words, triggers = await check_sample_transcription()
    
    print(f"\n🎯 Conclusion:")
    if len(triggers) <= 1:
        print(f"   The sample video only contains {len(triggers)} detectable gaming trigger(s).")
        print(f"   This explains why only 1 clip is being generated.")
        print(f"   The app is working correctly - it's just that this particular")
        print(f"   video doesn't have many clear English gaming announcements.")
        print(f"\n💡 To test with more triggers:")
        print(f"   1. Find a video with clear English gaming callouts")
        print(f"   2. Or record yourself saying multiple gaming phrases")
        print(f"   3. Upload that video to test the multi-clip functionality")
    else:
        print(f"   Found {len(triggers)} triggers - the app should create multiple clips!")

if __name__ == "__main__":
    asyncio.run(main())
