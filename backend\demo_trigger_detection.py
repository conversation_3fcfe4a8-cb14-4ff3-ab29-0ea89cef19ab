#!/usr/bin/env python3
"""
Demo script showing the new trigger detection system in action.
Run this to see how the multi-kill sequence detection works.
"""

from services.video_processor import VideoProcessor
from models import WordTimestamp


def create_sample_transcription():
    """Create a sample transcription with various gaming triggers"""
    return [
        # First sequence: Triple kill
        WordTimestamp(word="you", start=5.0, end=5.2),
        WordTimestamp(word="have", start=5.2, end=5.4),
        WordTimestamp(word="slain", start=5.4, end=5.7),
        WordTimestamp(word="an", start=5.7, end=5.8),
        WordTimestamp(word="enemy", start=5.8, end=6.1),
        WordTimestamp(word="double", start=6.5, end=6.8),
        WordTimestamp(word="kill", start=6.8, end=7.1),
        WordTimestamp(word="triple", start=7.5, end=7.8),
        WordTimestamp(word="kill", start=7.8, end=8.1),
        
        # Individual achievement
        WordTimestamp(word="godlike", start=15.0, end=15.5),
        
        # Another sequence: Mega kill
        WordTimestamp(word="you", start=25.0, end=25.2),
        WordTimestamp(word="have", start=25.2, end=25.4),
        WordTimestamp(word="slain", start=25.4, end=25.7),
        WordTimestamp(word="an", start=25.7, end=25.8),
        WordTimestamp(word="enemy", start=25.8, end=26.1),
        WordTimestamp(word="double", start=26.5, end=26.8),
        WordTimestamp(word="kill", start=26.8, end=27.1),
        WordTimestamp(word="triple", start=27.5, end=27.8),
        WordTimestamp(word="kill", start=27.8, end=28.1),
        WordTimestamp(word="mega", start=28.5, end=28.8),
        WordTimestamp(word="kill", start=28.8, end=29.1),
        
        # First blood
        WordTimestamp(word="first", start=35.0, end=35.3),
        WordTimestamp(word="blood", start=35.3, end=35.6),
        
        # Killing spree
        WordTimestamp(word="killing", start=45.0, end=45.4),
        WordTimestamp(word="spree", start=45.4, end=45.8),
        
        # Legendary
        WordTimestamp(word="legendary", start=55.0, end=55.5),
        
        # Monster kill
        WordTimestamp(word="monster", start=65.0, end=65.4),
        WordTimestamp(word="kill", start=65.4, end=65.7),
        
        # Unstoppable
        WordTimestamp(word="unstoppable", start=75.0, end=75.6),
    ]


def demo_trigger_detection():
    """Demonstrate the trigger detection system"""
    print("🎮 Video Highlight Extractor - Trigger Detection Demo")
    print("=" * 60)
    
    # Create processor
    processor = VideoProcessor()
    
    # Create sample transcription
    words = create_sample_transcription()
    
    print(f"\n📝 Sample Transcription ({len(words)} words):")
    print("   " + " ".join([w.word for w in words]))
    
    # Detect triggers
    print(f"\n🎯 Detecting triggers...")
    triggers = processor._find_triggers(words)
    
    print(f"\n✅ Found {len(triggers)} triggers:")
    print("-" * 40)
    
    for i, trigger in enumerate(triggers, 1):
        duration = trigger.end_time - trigger.start_time
        print(f"{i:2d}. {trigger.trigger}")
        print(f"    ⏰ Time: {trigger.timestamp:.1f}s")
        print(f"    📹 Clip: {trigger.start_time:.1f}s - {trigger.end_time:.1f}s ({duration:.1f}s)")
        
        if "sequence" in trigger.trigger:
            print(f"    🔗 Multi-kill sequence detected!")
        
        print()
    
    # Show sequence detection details
    sequence_triggers = [t for t in triggers if "sequence" in t.trigger]
    individual_triggers = [t for t in triggers if "sequence" not in t.trigger]
    
    print(f"📊 Summary:")
    print(f"   🔗 Multi-kill sequences: {len(sequence_triggers)}")
    print(f"   🎯 Individual triggers: {len(individual_triggers)}")
    
    if sequence_triggers:
        print(f"\n🔗 Multi-kill Sequences Detected:")
        for seq in sequence_triggers:
            print(f"   • {seq.trigger} ({seq.end_time - seq.start_time:.1f}s clip)")
    
    print(f"\n🎯 Individual Achievements:")
    for ind in individual_triggers:
        print(f"   • {ind.trigger} ({ind.end_time - ind.start_time:.1f}s clip)")
    
    print(f"\n🎬 Total highlight clips to be created: {len(triggers)}")
    
    # Show exact phrase matching demo
    print(f"\n🔍 Exact Phrase Matching Demo:")
    print("-" * 40)
    
    test_phrases = ["double kill", "triple kill", "you have slain an enemy", "godlike", "first blood"]
    
    for phrase in test_phrases:
        matches = processor._find_exact_phrase_matches(phrase, words)
        if matches:
            print(f"✅ '{phrase}' found {len(matches)} time(s)")
            for match in matches:
                print(f"   📍 At {match['timestamp']:.1f}s: \"{match['matched_text']}\"")
        else:
            print(f"❌ '{phrase}' not found")
    
    print(f"\n🎉 Demo complete! The system intelligently detects:")
    print(f"   • Exact phrase matches (not partial words)")
    print(f"   • Multi-kill sequences with extended clips")
    print(f"   • Individual achievements with optimal timing")
    print(f"   • Smart merging to avoid duplicate clips")


if __name__ == "__main__":
    demo_trigger_detection()
