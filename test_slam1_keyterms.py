#!/usr/bin/env python3
"""
Test slam-1 model with keyterms_prompt for gaming vocabulary
"""

import assemblyai as aai

# Set API key
aai.settings.api_key = "76943b85128e456b99358cce60d941f2"

def test_slam1_keyterms_configuration():
    """Test slam-1 model with keyterms_prompt"""
    print("🎤 Testing slam-1 with keyterms_prompt Configuration")
    print("=" * 55)
    
    try:
        # Test slam-1 configuration with keyterms_prompt
        config = aai.TranscriptionConfig(
            speech_model=aai.SpeechModel.slam_1,  # Use slam-1 model
            keyterms_prompt=[
                "double kill", "triple kill", "mega kill", "maniac", "savage",
                "first blood", "godlike", "legendary", "killing spree",
                "unstoppable", "monster kill", "you have slain an enemy",
                "alucard", "mobile legends", "mlbb", "moba", "hero", "champion",
                "kill", "enemy", "slain", "blood", "spree", "rampage"
            ]
        )
        
        print("✅ slam-1 with keyterms_prompt configuration created successfully!")
        print(f"📊 Speech model: {config.speech_model}")
        print(f"🎮 Gaming keyterms: {len(config.keyterms_prompt)} terms")
        
        # Show the keyterms
        print(f"\n🎯 Gaming keyterms for contextual understanding:")
        for i, term in enumerate(config.keyterms_prompt, 1):
            print(f"   {i:2d}. {term}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating slam-1 keyterms configuration: {e}")
        return False

def explain_keyterms_vs_word_boost():
    """Explain the difference between keyterms_prompt and word_boost"""
    print(f"\n📚 keyterms_prompt vs word_boost:")
    print("=" * 35)
    
    print("🔄 OLD (word_boost):")
    print("   • Simply increases likelihood of detecting specific words")
    print("   • Works with Universal and Nano models")
    print("   • Basic keyword matching")
    
    print("\n🚀 NEW (keyterms_prompt with slam-1):")
    print("   • Uses LLM contextual understanding")
    print("   • Understands semantic meaning and context")
    print("   • Enhances related terminology and variations")
    print("   • Improves contextually similar phrases")
    print("   • Only works with slam-1 model")
    
    print(f"\n💡 Benefits for gaming:")
    print("   • Better understanding of gaming context")
    print("   • Recognizes variations of gaming terms")
    print("   • Understands relationships between gaming phrases")
    print("   • Improved accuracy for gaming-specific vocabulary")

def show_slam1_keyterms_limits():
    """Show the limits and best practices for keyterms_prompt"""
    print(f"\n📋 keyterms_prompt Limits & Best Practices:")
    print("=" * 45)
    
    print("📊 Limits:")
    print("   • Maximum: 1000 words/phrases")
    print("   • Maximum: 6 words per phrase")
    print("   • Capitalization affects capacity")
    print("   • Longer words consume more capacity")
    
    print("\n✅ Best Practices:")
    print("   • Use shorter phrases when possible")
    print("   • Include variations of key terms")
    print("   • Focus on domain-specific vocabulary")
    print("   • Use lowercase for better capacity")
    
    print(f"\n🎮 Our Gaming Configuration:")
    print("   • 24 gaming-specific terms")
    print("   • Mix of phrases and single words")
    print("   • Covers all major gaming triggers")
    print("   • Well within the 1000 term limit")

def main():
    """Run slam-1 keyterms test"""
    print("🎮 slam-1 keyterms_prompt Test")
    print("=" * 35)
    
    # Test configuration
    success = test_slam1_keyterms_configuration()
    
    if success:
        explain_keyterms_vs_word_boost()
        show_slam1_keyterms_limits()
        
        print(f"\n🎉 slam-1 keyterms_prompt Ready!")
        print(f"=" * 35)
        print(f"✅ Configuration: Working")
        print(f"✅ Gaming keyterms: Loaded")
        print(f"✅ Model: slam-1 with LLM context")
        print(f"✅ Ready for: Enhanced gaming transcription")
        
        print(f"\n🚀 Expected Improvements:")
        print(f"   • Better contextual understanding of gaming terms")
        print(f"   • Recognition of gaming term variations")
        print(f"   • Enhanced accuracy for gaming vocabulary")
        print(f"   • Improved handling of gaming-specific phrases")
        
        print(f"\n🔄 Next Steps:")
        print(f"   1. Backend is already updated with keyterms_prompt")
        print(f"   2. Upload a gaming video to test the fix")
        print(f"   3. Should now work without errors")
        print(f"   4. Expect even better gaming trigger detection!")
        
    else:
        print(f"\n❌ Configuration failed!")

if __name__ == "__main__":
    main()
