#!/usr/bin/env python3
"""
Simple test to check what AssemblyAI transcribes from the sample video
"""

import assemblyai as aai
import os
from pathlib import Path

# Set API key
aai.settings.api_key = "76943b85128e456b99358cce60d941f2"

def find_sample_video():
    """Find the sample video file"""
    possible_paths = [
        "backend/samples/24 Kills + MANIAC!! Alucard Best Build Exp Lane!! - Build Top 1 Global Alucard ~ MLBB.mp4",
        "backend\\samples\\24 Kills + MANIAC!! Alucard Best Build Exp Lane!! - Build Top 1 Global Alucard ~ MLBB.mp4"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    # Try to find any MP4 in backend/samples
    samples_dir = Path("backend/samples")
    if samples_dir.exists():
        for file in samples_dir.glob("*.mp4"):
            return str(file)
    
    return None

def transcribe_sample_video():
    """Transcribe the sample video and show full results"""
    print("🎤 AssemblyAI Transcription Test")
    print("=" * 35)
    
    # Find video file
    video_path = find_sample_video()
    if not video_path:
        print("❌ No sample video found!")
        return
    
    print(f"📹 Video: {video_path}")
    print(f"📁 File exists: {os.path.exists(video_path)}")
    
    if not os.path.exists(video_path):
        print("❌ File not found!")
        return
    
    # Create transcriber
    transcriber = aai.Transcriber()
    
    # Configure for gaming content
    config = aai.TranscriptionConfig(
        word_boost=[
            "double kill", "triple kill", "mega kill", "maniac", "savage",
            "first blood", "godlike", "legendary", "killing spree", 
            "unstoppable", "monster kill", "you have slain an enemy"
        ],
        boost_param="high"
    )
    
    try:
        print("🔄 Starting transcription (this may take several minutes)...")
        transcript = transcriber.transcribe(video_path, config=config)
        
        if transcript.status == aai.TranscriptStatus.error:
            print(f"❌ Transcription failed: {transcript.error}")
            return
        
        print("✅ Transcription completed!")
        print(f"🆔 Transcript ID: {transcript.id}")
        
        # Show full text
        print(f"\n📝 FULL TRANSCRIPTION TEXT:")
        print("=" * 50)
        print(transcript.text)
        print("=" * 50)
        
        # Show word-level details
        if transcript.words:
            print(f"\n📊 WORD-BY-WORD BREAKDOWN:")
            print(f"Total words: {len(transcript.words)}")
            print("-" * 60)
            
            for i, word in enumerate(transcript.words):
                start_sec = word.start / 1000.0
                end_sec = word.end / 1000.0
                print(f"{i+1:3d}. '{word.text}' ({start_sec:.2f}s - {end_sec:.2f}s)")
        
        # Look for gaming words
        gaming_keywords = [
            "kill", "kills", "double", "triple", "mega", "maniac", "savage",
            "first", "blood", "enemy", "enemies", "slain", "slayed", 
            "spree", "legendary", "godlike", "unstoppable", "monster"
        ]
        
        found_gaming = []
        if transcript.words:
            for word in transcript.words:
                word_lower = word.text.lower().strip()
                if any(keyword in word_lower for keyword in gaming_keywords):
                    start_sec = word.start / 1000.0
                    found_gaming.append((word.text, start_sec))
        
        print(f"\n🎮 GAMING-RELATED WORDS FOUND:")
        print("-" * 40)
        if found_gaming:
            for word, timestamp in found_gaming:
                print(f"   • '{word}' at {timestamp:.2f}s")
        else:
            print("   ❌ No gaming keywords detected!")
        
        # Check for exact trigger phrases
        full_text_lower = transcript.text.lower()
        gaming_triggers = [
            "first blood", "double kill", "triple kill", "mega kill", 
            "maniac", "savage", "godlike", "legendary", "killing spree", 
            "unstoppable", "monster kill", "you have slain an enemy"
        ]
        
        found_triggers = []
        print(f"\n🎯 EXACT GAMING TRIGGERS FOUND:")
        print("-" * 40)
        for trigger in gaming_triggers:
            if trigger in full_text_lower:
                found_triggers.append(trigger)
                print(f"   ✅ '{trigger}'")
            else:
                print(f"   ❌ '{trigger}' - NOT FOUND")
        
        print(f"\n📊 SUMMARY:")
        print(f"   Total words transcribed: {len(transcript.words) if transcript.words else 0}")
        print(f"   Gaming-related words: {len(found_gaming)}")
        print(f"   Exact gaming triggers: {len(found_triggers)}")
        print(f"   Triggers found: {found_triggers}")
        
        if len(found_triggers) <= 1:
            print(f"\n💡 ANALYSIS:")
            print(f"   The video appears to have very few clear English gaming announcements.")
            print(f"   This explains why only 1 clip is being generated.")
            print(f"   The video might contain:")
            print(f"   - Background music/sound effects")
            print(f"   - Non-English gaming sounds")
            print(f"   - Unclear audio that AssemblyAI can't transcribe")
        else:
            print(f"\n🚨 ISSUE FOUND:")
            print(f"   Multiple triggers detected but app only created 1 clip!")
            print(f"   This indicates a bug in the trigger detection or clip creation.")
        
        return transcript
        
    except Exception as e:
        print(f"❌ Error during transcription: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    transcribe_sample_video()
