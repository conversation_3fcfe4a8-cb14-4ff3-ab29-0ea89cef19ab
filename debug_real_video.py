#!/usr/bin/env python3
"""
Debug script to check what's happening with real video processing
"""

import requests
import time
import json

API_URL = "http://localhost:8001"

def check_recent_job_logs():
    """Check logs from the most recent job"""
    print("🔍 Checking Recent Job Processing...")
    print("=" * 40)
    
    # This would require access to the job files
    # For now, let's create a simple test
    
    print("💡 To debug the real issue:")
    print("1. Upload a gaming video through the web interface")
    print("2. Check the backend terminal for detailed logs")
    print("3. Look for these debug messages:")
    print("   - '=== FULL WORD-BY-WORD TRANSCRIPTION ==='")
    print("   - '🎮 Found potential gaming words:'")
    print("   - '🔍 Searching for gaming triggers...'")
    print("   - '📊 Total individual triggers found:'")
    print()
    
    print("🎯 Common Issues & Solutions:")
    print("1. ❌ No gaming words detected:")
    print("   → Video might not have clear gaming audio")
    print("   → Try a video with clear kill announcements")
    print()
    print("2. ❌ Gaming words found but no triggers:")
    print("   → Exact phrase matching might be too strict")
    print("   → Fuzzy matching should kick in automatically")
    print()
    print("3. ❌ Triggers found but only 1 clip:")
    print("   → Check if clips are being merged")
    print("   → Our fix should prevent this")

def test_api_endpoints():
    """Test if the API is working with our fixes"""
    print("\n🔧 Testing API Endpoints...")
    print("=" * 30)
    
    try:
        # Test health endpoint
        response = requests.get(f"{API_URL}/health")
        if response.status_code == 200:
            print("✅ Backend API: Connected")
        else:
            print("❌ Backend API: Not responding")
            return False
            
        # Test root endpoint
        response = requests.get(f"{API_URL}/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Version: {data.get('version', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ API Error: {e}")
        return False

def create_test_gaming_audio():
    """Instructions for creating test gaming audio"""
    print("\n🎮 Creating Test Gaming Audio...")
    print("=" * 35)
    
    print("📝 To test with guaranteed gaming triggers:")
    print()
    print("Option 1 - Record yourself saying:")
    print('   "First blood! You have slain an enemy."')
    print('   "Double kill! Triple kill!"')
    print('   "Godlike! Legendary!"')
    print('   "Killing spree! Unstoppable!"')
    print()
    print("Option 2 - Use text-to-speech:")
    print("   1. Go to https://ttsmp3.com/")
    print("   2. Enter the gaming phrases above")
    print("   3. Download as MP3")
    print("   4. Convert to MP4 (add silent video)")
    print()
    print("Option 3 - Find gaming clips:")
    print("   1. Search YouTube for 'gaming kill announcements'")
    print("   2. Download a short clip with clear audio")
    print("   3. Make sure it has multiple kill callouts")

def show_debugging_steps():
    """Show step-by-step debugging process"""
    print("\n🔧 Debugging Steps:")
    print("=" * 20)
    
    print("1. 🚀 Start the backend with our fixes:")
    print("   python -c \"import sys; sys.path.append('backend'); from main import app; import uvicorn; uvicorn.run(app, host='0.0.0.0', port=8001)\"")
    print()
    
    print("2. 🌐 Open the frontend:")
    print("   http://localhost:3000")
    print()
    
    print("3. 📹 Upload a gaming video and watch the backend terminal for:")
    print("   - Full transcription with timestamps")
    print("   - Gaming words detection")
    print("   - Trigger matching attempts")
    print("   - Number of clips created")
    print()
    
    print("4. 🔍 If still only 1 clip, check:")
    print("   - Is AssemblyAI transcribing correctly?")
    print("   - Are gaming words being detected?")
    print("   - Are triggers being found?")
    print("   - Are clips being created properly?")

def main():
    """Main debugging function"""
    print("🎮 Video Highlight Extractor - Debug Tool")
    print("=" * 45)
    
    # Test API
    api_working = test_api_endpoints()
    
    if not api_working:
        print("\n❌ Backend not running! Start it first:")
        print("python -c \"import sys; sys.path.append('backend'); from main import app; import uvicorn; uvicorn.run(app, host='0.0.0.0', port=8001)\"")
        return
    
    # Show debugging info
    check_recent_job_logs()
    create_test_gaming_audio()
    show_debugging_steps()
    
    print("\n🎯 Next Steps:")
    print("1. Start the backend if not running")
    print("2. Upload a gaming video with clear audio")
    print("3. Watch the backend terminal for debug output")
    print("4. Report what you see in the logs!")

if __name__ == "__main__":
    main()
