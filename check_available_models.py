#!/usr/bin/env python3
"""
Check what speech models are available in AssemblyAI
"""

import assemblyai as aai

def check_available_models():
    """Check what speech models are available"""
    print("🔍 Checking Available AssemblyAI Speech Models")
    print("=" * 50)
    
    try:
        # Check if SpeechModel enum exists and what attributes it has
        if hasattr(aai, 'SpeechModel'):
            speech_model = aai.SpeechModel
            print("✅ SpeechModel enum found!")
            
            # Get all available models
            models = [attr for attr in dir(speech_model) if not attr.startswith('_')]
            print(f"📊 Available models: {len(models)}")
            
            for i, model in enumerate(models, 1):
                try:
                    model_value = getattr(speech_model, model)
                    print(f"   {i:2d}. {model} = {model_value}")
                except Exception as e:
                    print(f"   {i:2d}. {model} (error: {e})")
        else:
            print("❌ SpeechModel enum not found in this version")
            
        # Check AssemblyAI version
        if hasattr(aai, '__version__'):
            print(f"\n📦 AssemblyAI version: {aai.__version__}")
        else:
            print(f"\n📦 AssemblyAI version: Unknown")
            
        # Try to create a basic config to see what works
        print(f"\n🧪 Testing basic TranscriptionConfig...")
        try:
            config = aai.TranscriptionConfig(
                word_boost=["test"],
                boost_param="high"
            )
            print("✅ Basic config works!")
            
            # Check if we can access speech_model parameter
            if hasattr(config, 'speech_model'):
                print(f"✅ speech_model parameter available: {config.speech_model}")
            else:
                print("❌ speech_model parameter not available")
                
        except Exception as e:
            print(f"❌ Basic config failed: {e}")
            
    except Exception as e:
        print(f"❌ Error checking models: {e}")

def suggest_alternatives():
    """Suggest alternative configurations"""
    print(f"\n💡 Alternative Configurations:")
    print("=" * 30)
    
    print("Option 1: Use default model (recommended)")
    print("```python")
    print("config = aai.TranscriptionConfig(")
    print("    word_boost=[...],")
    print("    boost_param='high'")
    print(")")
    print("```")
    
    print("\nOption 2: Check for newer model names")
    print("- slam_1 might be named differently")
    print("- Check AssemblyAI documentation")
    print("- Update library: pip install --upgrade assemblyai")
    
    print("\nOption 3: Use latest available model")
    print("- The default model is usually the latest")
    print("- AssemblyAI automatically uses best available")

if __name__ == "__main__":
    check_available_models()
    suggest_alternatives()
