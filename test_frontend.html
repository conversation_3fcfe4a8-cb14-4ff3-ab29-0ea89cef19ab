<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MLClips - Mobile Legends Highlight Extractor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .upload-zone {
            border: 2px dashed #fff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-zone:hover {
            background: rgba(255, 255, 255, 0.1);
        }
        .upload-zone.dragover {
            background: rgba(255, 255, 255, 0.2);
            border-color: #4CAF50;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #45a049;
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: #4CAF50;
            width: 0%;
            transition: width 0.3s ease;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
        }
        .clips {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .clip {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
        }
        video {
            width: 100%;
            border-radius: 5px;
        }
        .triggers {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
        }
        .trigger {
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 Video Highlight Extractor</h1>
        <p>Upload your gaming videos to automatically extract highlight clips using AI speech recognition!</p>
        
        <div class="triggers">
            <div class="trigger">Double Kill</div>
            <div class="trigger">Triple Kill</div>
            <div class="trigger">Mega Kill</div>
            <div class="trigger">Maniac</div>
            <div class="trigger">Savage</div>
            <div class="trigger">First Blood</div>
            <div class="trigger">Godlike</div>
            <div class="trigger">Legendary</div>
            <div class="trigger">Killing Spree</div>
            <div class="trigger">Unstoppable</div>
            <div class="trigger">Monster Kill</div>
        </div>

        <div class="upload-zone" id="uploadZone">
            <div style="font-size: 48px; margin-bottom: 20px;">🎬</div>
            <h3>Drag & Drop Your Gaming Video</h3>
            <p>or click to browse files</p>
            <p style="font-size: 12px; opacity: 0.8;">Supports MP4, AVI, MOV, MKV, WebM</p>
            <input type="file" id="fileInput" accept="video/*" style="display: none;">
        </div>

        <div id="uploadSection" style="display: none;">
            <h3 id="fileName"></h3>
            <button onclick="uploadVideo()">Upload & Process Video</button>
            <button onclick="resetUpload()">Cancel</button>
            
            <div id="uploadProgress" style="display: none;">
                <p>Uploading...</p>
                <div class="progress">
                    <div class="progress-bar" id="progressBar"></div>
                </div>
                <span id="progressText">0%</span>
            </div>
        </div>

        <div id="statusSection" style="display: none;">
            <div class="status">
                <h3>Processing Status</h3>
                <div id="statusContent"></div>
                <div id="processingProgress" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar" id="processingBar"></div>
                    </div>
                    <span id="processingText">0%</span>
                </div>
            </div>
        </div>

        <div id="clipsSection" style="display: none;">
            <h3>🎯 Highlight Clips</h3>
            <div class="clips" id="clipsList"></div>
        </div>

        <div id="apiStatus" style="margin-top: 30px; padding: 15px; background: rgba(0,0,0,0.2); border-radius: 5px;">
            <h4>🔧 System Status</h4>
            <p id="apiStatusText">Checking API connection...</p>
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:8001';
        let currentJobId = null;
        let selectedFile = null;
        let statusInterval = null;

        // Check API status on load
        window.onload = function() {
            checkApiStatus();
        };

        async function checkApiStatus() {
            try {
                const response = await fetch(`${API_URL}/health`);
                const data = await response.json();
                document.getElementById('apiStatusText').innerHTML = 
                    `✅ Backend API: Connected<br>📊 Status: ${data.status}`;
            } catch (error) {
                document.getElementById('apiStatusText').innerHTML = 
                    `❌ Backend API: Disconnected<br>🔧 Make sure the backend is running on port 8001`;
            }
        }

        // File upload handling
        const uploadZone = document.getElementById('uploadZone');
        const fileInput = document.getElementById('fileInput');

        uploadZone.addEventListener('click', () => fileInput.click());
        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.classList.add('dragover');
        });
        uploadZone.addEventListener('dragleave', () => {
            uploadZone.classList.remove('dragover');
        });
        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });

        function handleFileSelect(file) {
            if (!file.type.startsWith('video/')) {
                alert('Please select a video file');
                return;
            }
            
            selectedFile = file;
            document.getElementById('fileName').textContent = `${file.name} (${(file.size / 1024 / 1024).toFixed(1)} MB)`;
            document.getElementById('uploadZone').style.display = 'none';
            document.getElementById('uploadSection').style.display = 'block';
        }

        async function uploadVideo() {
            if (!selectedFile) return;

            const formData = new FormData();
            formData.append('file', selectedFile);

            document.getElementById('uploadProgress').style.display = 'block';

            try {
                const xhr = new XMLHttpRequest();
                
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const progress = Math.round((e.loaded / e.total) * 100);
                        document.getElementById('progressBar').style.width = progress + '%';
                        document.getElementById('progressText').textContent = progress + '%';
                    }
                });

                xhr.onload = function() {
                    if (xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);
                        currentJobId = response.job_id;
                        
                        document.getElementById('uploadSection').style.display = 'none';
                        document.getElementById('statusSection').style.display = 'block';
                        
                        startStatusPolling();
                    } else {
                        alert('Upload failed: ' + xhr.responseText);
                    }
                };

                xhr.onerror = function() {
                    alert('Upload failed: Network error');
                };

                xhr.open('POST', `${API_URL}/upload`);
                xhr.send(formData);

            } catch (error) {
                alert('Upload failed: ' + error.message);
            }
        }

        async function startStatusPolling() {
            statusInterval = setInterval(async () => {
                try {
                    const response = await fetch(`${API_URL}/status/${currentJobId}`);
                    const status = await response.json();
                    
                    updateStatus(status);
                    
                    if (status.status === 'completed' || status.status === 'failed') {
                        clearInterval(statusInterval);
                    }
                } catch (error) {
                    console.error('Status polling error:', error);
                }
            }, 3000);
        }

        function updateStatus(status) {
            const statusContent = document.getElementById('statusContent');
            const processingProgress = document.getElementById('processingProgress');
            const processingBar = document.getElementById('processingBar');
            const processingText = document.getElementById('processingText');
            const clipsSection = document.getElementById('clipsSection');

            if (status.status === 'processing') {
                statusContent.innerHTML = '🔄 Processing your video...';
                processingProgress.style.display = 'block';
                processingBar.style.width = status.progress + '%';
                processingText.textContent = status.progress + '%';
            } else if (status.status === 'completed') {
                statusContent.innerHTML = '✅ Processing complete!';
                processingProgress.style.display = 'none';
                
                if (status.clips && status.clips.length > 0) {
                    displayClips(status.clips);
                } else {
                    statusContent.innerHTML += '<br>ℹ️ No gaming triggers found in this video.';
                }
            } else if (status.status === 'failed') {
                statusContent.innerHTML = '❌ Processing failed: ' + (status.error || 'Unknown error');
                processingProgress.style.display = 'none';
            }
        }

        function displayClips(clips) {
            const clipsSection = document.getElementById('clipsSection');
            const clipsList = document.getElementById('clipsList');
            
            clipsList.innerHTML = '';
            
            clips.forEach((clipPath, index) => {
                const clipDiv = document.createElement('div');
                clipDiv.className = 'clip';
                clipDiv.innerHTML = `
                    <h4>Highlight ${index + 1}</h4>
                    <video controls>
                        <source src="${API_URL}${clipPath}" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                    <br>
                    <a href="${API_URL}${clipPath}" download="highlight_${index + 1}.mp4">
                        <button>📥 Download</button>
                    </a>
                `;
                clipsList.appendChild(clipDiv);
            });
            
            clipsSection.style.display = 'block';
        }

        function resetUpload() {
            selectedFile = null;
            currentJobId = null;
            if (statusInterval) {
                clearInterval(statusInterval);
                statusInterval = null;
            }
            
            document.getElementById('uploadZone').style.display = 'block';
            document.getElementById('uploadSection').style.display = 'none';
            document.getElementById('statusSection').style.display = 'none';
            document.getElementById('clipsSection').style.display = 'none';
            document.getElementById('uploadProgress').style.display = 'none';
            
            // Reset progress bars
            document.getElementById('progressBar').style.width = '0%';
            document.getElementById('progressText').textContent = '0%';
        }
    </script>
</body>
</html>
