import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
import tempfile
import os

from main import app

client = TestClient(app)


def test_root_endpoint():
    """Test the root endpoint"""
    response = client.get("/")
    assert response.status_code == 200
    assert response.json()["message"] == "Video Highlight Extractor API"


def test_health_endpoint():
    """Test the health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"


def test_upload_invalid_file():
    """Test upload with invalid file type"""
    with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as tmp_file:
        tmp_file.write(b"This is not a video file")
        tmp_file.flush()
        
        with open(tmp_file.name, "rb") as f:
            response = client.post(
                "/upload",
                files={"file": ("test.txt", f, "text/plain")}
            )
    
    os.unlink(tmp_file.name)
    assert response.status_code == 400
    assert "File must be a video" in response.json()["detail"]


@patch('main.video_processor.process_video')
def test_upload_valid_video(mock_process):
    """Test upload with valid video file"""
    mock_process.return_value = None
    
    # Create a fake video file
    video_content = b"fake video content"
    
    response = client.post(
        "/upload",
        files={"file": ("test.mp4", video_content, "video/mp4")}
    )
    
    assert response.status_code == 200
    assert "job_id" in response.json()
    assert response.json()["message"] == "Video uploaded successfully. Processing started."


def test_status_not_found():
    """Test status endpoint with non-existent job ID"""
    response = client.get("/status/non-existent-job-id")
    assert response.status_code == 404
    assert response.json()["detail"] == "Job not found"


@patch('main.job_manager.get_job_status')
def test_status_found(mock_get_status):
    """Test status endpoint with existing job ID"""
    mock_status = MagicMock()
    mock_status.progress = 50
    mock_status.clips = []
    mock_status.status = "processing"
    mock_status.error = None
    mock_get_status.return_value = mock_status
    
    response = client.get("/status/test-job-id")
    assert response.status_code == 200
    data = response.json()
    assert data["progress"] == 50
    assert data["status"] == "processing"
