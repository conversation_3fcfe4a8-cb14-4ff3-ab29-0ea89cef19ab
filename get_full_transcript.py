#!/usr/bin/env python3
"""
Get the full transcript from the most recent job and analyze it
"""

import json
import os
from pathlib import Path
import assemblyai as aai

# Set API key
aai.settings.api_key = "76943b85128e456b99358cce60d941f2"

def find_most_recent_job():
    """Find the most recent job file"""
    jobs_dir = Path("jobs")
    if not jobs_dir.exists():
        return None
    
    job_files = list(jobs_dir.glob("*.json"))
    if not job_files:
        return None
    
    # Get the most recent job file
    most_recent = max(job_files, key=lambda f: f.stat().st_mtime)
    return most_recent

def get_transcript_from_assemblyai():
    """Get transcript directly from AssemblyAI using the most recent job"""
    print("🔍 Looking for recent job with transcript ID...")
    
    job_file = find_most_recent_job()
    if not job_file:
        print("❌ No job files found")
        return None
    
    print(f"📁 Found job file: {job_file.name}")
    
    # Read job file to get transcript ID or other info
    with open(job_file, 'r') as f:
        job_data = json.load(f)
    
    print(f"📊 Job status: {job_data.get('status', 'unknown')}")
    print(f"📊 Job progress: {job_data.get('progress', 0)}%")
    
    # The job file might not have the transcript ID stored
    # Let's try to find any video file that was processed recently
    
    # Check uploads directory for recent files
    uploads_dir = Path("uploads")
    if uploads_dir.exists():
        video_files = []
        for ext in ["*.mp4", "*.avi", "*.mov", "*.mkv"]:
            video_files.extend(uploads_dir.glob(ext))
        
        if video_files:
            # Get most recent video
            recent_video = max(video_files, key=lambda f: f.stat().st_mtime)
            print(f"📹 Found recent video: {recent_video.name}")
            return str(recent_video)
    
    # Check if there are any video files in the project
    for root, _, files in os.walk("."):
        for file in files:
            if file.endswith(('.mp4', '.avi', '.mov', '.mkv')):
                video_path = os.path.join(root, file)
                print(f"📹 Found video file: {video_path}")
                return video_path
    
    print("❌ No video files found to transcribe")
    return None

def transcribe_and_save_full_transcript():
    """Transcribe video and save full transcript to markdown"""
    print("🎤 Getting Full Transcript for Analysis")
    print("=" * 40)
    
    # Try to find a video to transcribe
    video_path = get_transcript_from_assemblyai()
    if not video_path:
        print("❌ No video found to transcribe")
        return
    
    if not os.path.exists(video_path):
        print(f"❌ Video file not found: {video_path}")
        return
    
    print(f"📹 Transcribing: {os.path.basename(video_path)}")
    
    # Create transcriber with gaming vocabulary boost using best available model
    transcriber = aai.Transcriber()
    config = aai.TranscriptionConfig(
        speech_model=aai.SpeechModel.best,  # Use best available model for highest accuracy
        word_boost=[
            "double kill", "triple kill", "mega kill", "maniac", "savage",
            "first blood", "godlike", "legendary", "killing spree",
            "unstoppable", "monster kill", "you have slain an enemy",
            "alucard", "mobile legends", "mlbb", "moba", "kill", "kills", "enemy", "enemies"
        ],
        boost_param="high"
    )
    
    try:
        print("🔄 Starting transcription (this may take several minutes)...")
        transcript = transcriber.transcribe(video_path, config=config)
        
        if transcript.status == aai.TranscriptStatus.error:
            print(f"❌ Transcription failed: {transcript.error}")
            return
        
        print("✅ Transcription completed!")
        
        # Create markdown file with full analysis
        markdown_content = f"""# Full Video Transcript Analysis

## Video Information
- **File**: {os.path.basename(video_path)}
- **Transcript ID**: {transcript.id}
- **Status**: {transcript.status}

## Full Transcript Text
```
{transcript.text}
```

## Word-by-Word Breakdown
| # | Word | Start (s) | End (s) | Duration (s) |
|---|------|-----------|---------|--------------|
"""
        
        # Add word-by-word breakdown
        if transcript.words:
            for i, word in enumerate(transcript.words, 1):
                start_sec = word.start / 1000.0
                end_sec = word.end / 1000.0
                duration = end_sec - start_sec
                markdown_content += f"| {i} | {word.text} | {start_sec:.2f} | {end_sec:.2f} | {duration:.2f} |\n"
        
        # Analyze for gaming content
        markdown_content += f"""

## Gaming Content Analysis

### Gaming Keywords Found
"""
        
        gaming_keywords = [
            "kill", "kills", "double", "triple", "mega", "maniac", "savage",
            "first", "blood", "enemy", "enemies", "slain", "slayed", 
            "spree", "legendary", "godlike", "unstoppable", "monster",
            "alucard", "mobile", "legends", "hero", "champion"
        ]
        
        found_gaming = []
        if transcript.words:
            for word in transcript.words:
                word_lower = word.text.lower().strip()
                if any(keyword in word_lower for keyword in gaming_keywords):
                    start_sec = word.start / 1000.0
                    found_gaming.append((word.text, start_sec))
        
        if found_gaming:
            for word, timestamp in found_gaming:
                markdown_content += f"- **{word}** at {timestamp:.2f}s\n"
        else:
            markdown_content += "- ❌ No gaming keywords detected\n"
        
        # Check for exact gaming triggers
        markdown_content += f"""

### Exact Gaming Triggers Check
"""
        
        full_text_lower = transcript.text.lower()
        gaming_triggers = [
            "first blood", "double kill", "triple kill", "mega kill", 
            "maniac", "savage", "godlike", "legendary", "killing spree", 
            "unstoppable", "monster kill", "you have slain an enemy"
        ]
        
        found_triggers = []
        for trigger in gaming_triggers:
            if trigger in full_text_lower:
                found_triggers.append(trigger)
                markdown_content += f"- ✅ **{trigger}** - FOUND\n"
            else:
                markdown_content += f"- ❌ {trigger} - NOT FOUND\n"
        
        # Summary
        markdown_content += f"""

## Summary
- **Total Words**: {len(transcript.words) if transcript.words else 0}
- **Gaming Keywords**: {len(found_gaming)}
- **Exact Gaming Triggers**: {len(found_triggers)}
- **Triggers Found**: {found_triggers}

## Analysis
"""
        
        if len(found_triggers) <= 1:
            markdown_content += """
The video appears to have very few clear English gaming announcements that match our exact trigger phrases. This explains why only 1 clip is being generated.

**Possible reasons:**
1. Gaming announcements are in a different language
2. Audio quality makes speech recognition difficult
3. Gaming sounds are background/ambient rather than clear speech
4. The video content doesn't match the title expectations
"""
        else:
            markdown_content += f"""
🚨 **ISSUE DETECTED**: Multiple gaming triggers found in transcript but app only created 1 clip!

**Found {len(found_triggers)} triggers**: {', '.join(found_triggers)}

This indicates a bug in our trigger detection or clip creation logic that needs investigation.
"""
        
        # Save to markdown file
        with open("full_transcript_analysis.md", "w", encoding="utf-8") as f:
            f.write(markdown_content)
        
        print(f"✅ Full transcript saved to: full_transcript_analysis.md")
        print(f"📊 Summary:")
        print(f"   Total words: {len(transcript.words) if transcript.words else 0}")
        print(f"   Gaming keywords: {len(found_gaming)}")
        print(f"   Exact triggers: {len(found_triggers)}")
        print(f"   Triggers: {found_triggers}")
        
        return transcript
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    transcribe_and_save_full_transcript()
