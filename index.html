<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MLClips - Redirecting...</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            text-align: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid #fff;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    <script>
        // Check if Next.js app is running on port 3000
        function checkNextApp() {
            fetch('http://localhost:3000')
                .then(response => {
                    if (response.ok) {
                        window.location.href = 'http://localhost:3000';
                    } else {
                        setTimeout(checkNextApp, 1000);
                    }
                })
                .catch(() => {
                    setTimeout(checkNextApp, 1000);
                });
        }
        
        // Start checking immediately
        checkNextApp();
        
        // Fallback redirect after 5 seconds
        setTimeout(() => {
            window.location.href = 'http://localhost:3000';
        }, 5000);
    </script>
</head>
<body>
    <div class="container">
        <h1>🎮 MLClips</h1>
        <p><strong>Capture and share your best Mobile Legends moments in seconds.</strong></p>
        <div class="spinner"></div>
        <p>Redirecting to MLClips app...</p>
        <p><small>If you're not redirected automatically, <a href="http://localhost:3000" style="color: #87CEEB;">click here</a></small></p>
    </div>
</body>
</html>
