#!/usr/bin/env python3
"""
Simple script to start both backend and frontend servers
"""

import subprocess
import sys
import time
import threading
import http.server
import socketserver
import os
from pathlib import Path

def start_backend():
    """Start the FastAPI backend server"""
    print("🚀 Starting Backend API on port 8001...")
    
    # Add backend to Python path
    backend_path = Path(__file__).parent / "backend"
    sys.path.insert(0, str(backend_path))
    
    try:
        from main import app
        import uvicorn
        uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")
    except Exception as e:
        print(f"❌ Backend failed to start: {e}")

def start_frontend():
    """Start a simple HTTP server for the frontend"""
    print("🌐 Starting Frontend on port 3000...")
    time.sleep(2)  # Wait for backend to start
    
    # Change to the directory containing our HTML file
    os.chdir(Path(__file__).parent)
    
    class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
        def end_headers(self):
            # Add CORS headers
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', '*')
            super().end_headers()
        
        def do_GET(self):
            # Serve our main HTML file for the root path
            if self.path == '/' or self.path == '/index.html':
                self.path = '/test_frontend.html'
            super().do_GET()
    
    try:
        with socketserver.TCPServer(("", 3000), CustomHTTPRequestHandler) as httpd:
            print("✅ Frontend server running at: http://localhost:3000")
            print("✅ Backend API running at: http://localhost:8001")
            print("✅ API Docs available at: http://localhost:8001/docs")
            print("\n🎮 Your Video Highlight Extractor is ready!")
            print("📱 Open http://localhost:3000 in your browser")
            print("\n⏹️  Press Ctrl+C to stop both servers")
            httpd.serve_forever()
    except Exception as e:
        print(f"❌ Frontend failed to start: {e}")

def main():
    """Start both servers"""
    print("🎮 Video Highlight Extractor - Starting Servers")
    print("=" * 50)
    
    # Start backend in a separate thread
    backend_thread = threading.Thread(target=start_backend, daemon=True)
    backend_thread.start()
    
    # Start frontend in main thread (so Ctrl+C works)
    try:
        start_frontend()
    except KeyboardInterrupt:
        print("\n\n🛑 Shutting down servers...")
        print("👋 Thanks for using Video Highlight Extractor!")

if __name__ == "__main__":
    main()
