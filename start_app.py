#!/usr/bin/env python3
"""
Simple script to start both backend and frontend servers
"""

import subprocess
import sys
import time
import threading
from pathlib import Path

def start_backend():
    """Start the FastAPI backend server"""
    print("🚀 Starting Backend API on port 8001...")
    
    # Add backend to Python path
    backend_path = Path(__file__).parent / "backend"
    sys.path.insert(0, str(backend_path))
    
    try:
        from main import app
        import uvicorn
        uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")
    except Exception as e:
        print(f"❌ Backend failed to start: {e}")

def start_frontend():
    """Start the Next.js frontend"""
    print("🌐 Starting Next.js Frontend on port 3000...")
    time.sleep(3)  # Wait for backend to start

    # Change to the frontend directory
    frontend_dir = Path(__file__).parent / "frontend"

    try:
        # Start Next.js development server
        process = subprocess.Popen(
            ["npm", "run", "dev"],
            cwd=frontend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )

        print("✅ Frontend server starting at: http://localhost:3000")
        print("✅ Backend API running at: http://localhost:8001")
        print("✅ API Docs available at: http://localhost:8001/docs")
        print("\n🎮 Your MLClips app is ready!")
        print("📱 Open http://localhost:3000 in your browser")
        print("\n⏹️  Press Ctrl+C to stop both servers")

        # Wait for the process and show output
        try:
            for line in process.stdout:
                if line.strip():
                    print(f"Frontend: {line.strip()}")
        except KeyboardInterrupt:
            process.terminate()
            process.wait()

    except Exception as e:
        print(f"❌ Frontend failed to start: {e}")
        print("💡 Make sure you have Node.js installed and run 'npm install' in the frontend directory")

def main():
    """Start both servers"""
    print("🎮 MLClips - Starting Servers")
    print("=" * 30)
    
    # Start backend in a separate thread
    backend_thread = threading.Thread(target=start_backend, daemon=True)
    backend_thread.start()
    
    # Start frontend in main thread (so Ctrl+C works)
    try:
        start_frontend()
    except KeyboardInterrupt:
        print("\n\n🛑 Shutting down servers...")
        print("👋 Thanks for using MLClips!")

if __name__ == "__main__":
    main()
