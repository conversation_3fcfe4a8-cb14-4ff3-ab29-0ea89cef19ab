"""Custom exceptions for the video processing service"""


class VideoProcessingError(Exception):
    """Base exception for video processing errors"""
    pass


class TranscriptionError(VideoProcessingError):
    """Raised when speech-to-text transcription fails"""
    pass


class FFmpegError(VideoProcessingError):
    """Raised when FFmpeg operations fail"""
    pass


class HuggingFaceAPIError(VideoProcessingError):
    """Raised when Hugging Face API calls fail"""
    pass


class InvalidVideoFormatError(VideoProcessingError):
    """Raised when video format is not supported"""
    pass


class JobNotFoundError(Exception):
    """Raised when a job ID is not found"""
    pass
