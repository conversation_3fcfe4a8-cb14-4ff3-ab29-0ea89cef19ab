services:
  # Backend API Service
  - type: web
    name: video-highlight-api
    env: python
    buildCommand: "cd backend && pip install -r requirements.txt"
    startCommand: "cd backend && uvicorn main:app --host 0.0.0.0 --port $PORT"
    plan: starter
    healthCheckPath: /health
    envVars:
      - key: HF_ENDPOINT
        sync: false
      - key: HF_TOKEN
        sync: false
      - key: CORS_ORIGINS
        value: https://video-highlight-frontend.onrender.com
    disk:
      name: video-storage
      mountPath: /opt/render/project/src/backend/uploads
      sizeGB: 10

  # Frontend Static Site
  - type: web
    name: video-highlight-frontend
    env: static
    buildCommand: "cd frontend && npm install && npm run build"
    staticPublishPath: frontend/out
    plan: starter
    buildCommand: "cd frontend && npm install && npm run build && npm run export"
    envVars:
      - key: NEXT_PUBLIC_API_URL
        value: https://video-highlight-api.onrender.com

databases:
  # Optional: Add Redis for job queue if needed
  # - name: redis
  #   plan: starter
