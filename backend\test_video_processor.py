import pytest
from unittest.mock import AsyncMock, MagicMock, patch
import tempfile
import os
from pathlib import Path

from services.video_processor import VideoProcessor
from services.job_manager import JobManager
from models import WordTimestamp, TriggerMatch, ClipRange
from services.exceptions import TranscriptionError, FFmpegError, HuggingFaceAPIError


@pytest.fixture
def video_processor():
    return VideoProcessor()


@pytest.fixture
def job_manager():
    return JobManager()


@pytest.fixture
def sample_words():
    return [
        WordTimestamp(word="you", start=1.0, end=1.2),
        WordTimestamp(word="have", start=1.2, end=1.4),
        WordTimestamp(word="slain", start=1.4, end=1.7),
        WordTimestamp(word="an", start=1.7, end=1.8),
        WordTimestamp(word="enemy", start=1.8, end=2.1),
        WordTimestamp(word="double", start=2.5, end=2.8),
        WordTimestamp(word="kill", start=2.8, end=3.1),
        WordTimestamp(word="triple", start=3.5, end=3.8),
        WordTimestamp(word="kill", start=3.8, end=4.1),
        WordTimestamp(word="first", start=10.0, end=10.3),
        WordTimestamp(word="blood", start=10.3, end=10.6),
        WordTimestamp(word="godlike", start=15.0, end=15.5),
    ]

@pytest.fixture
def multi_kill_sequence_words():
    return [
        WordTimestamp(word="you", start=1.0, end=1.2),
        WordTimestamp(word="have", start=1.2, end=1.4),
        WordTimestamp(word="slain", start=1.4, end=1.7),
        WordTimestamp(word="an", start=1.7, end=1.8),
        WordTimestamp(word="enemy", start=1.8, end=2.1),
        WordTimestamp(word="double", start=2.5, end=2.8),
        WordTimestamp(word="kill", start=2.8, end=3.1),
        WordTimestamp(word="triple", start=3.5, end=3.8),
        WordTimestamp(word="kill", start=3.8, end=4.1),
        WordTimestamp(word="mega", start=4.5, end=4.8),
        WordTimestamp(word="kill", start=4.8, end=5.1),
    ]


class TestVideoProcessor:
    def test_find_exact_phrase_matches(self, video_processor, sample_words):
        """Test exact phrase matching"""
        matches = video_processor._find_exact_phrase_matches("double kill", sample_words)
        assert len(matches) == 1
        assert matches[0]['matched_text'] == "double kill"

        matches = video_processor._find_exact_phrase_matches("you have slain an enemy", sample_words)
        assert len(matches) == 1
        assert matches[0]['matched_text'] == "you have slain an enemy"

        # Test non-existent phrase
        matches = video_processor._find_exact_phrase_matches("quad kill", sample_words)
        assert len(matches) == 0

    def test_find_triggers_individual(self, video_processor, sample_words):
        """Test individual trigger detection"""
        triggers = video_processor._find_triggers(sample_words)

        # Should find individual triggers
        trigger_names = [t.trigger for t in triggers]
        assert "double kill" in trigger_names or "triple kill sequence" in trigger_names
        assert "first blood" in trigger_names
        assert "godlike" in trigger_names

    def test_multi_kill_sequence_detection(self, video_processor, multi_kill_sequence_words):
        """Test multi-kill sequence detection"""
        triggers = video_processor._find_triggers(multi_kill_sequence_words)

        # Should detect a mega kill sequence
        sequence_triggers = [t for t in triggers if "sequence" in t.trigger]
        assert len(sequence_triggers) > 0

        # Check that sequence trigger covers the full range
        if sequence_triggers:
            seq_trigger = sequence_triggers[0]
            assert seq_trigger.start_time < seq_trigger.end_time
            assert "mega kill" in seq_trigger.trigger or "triple kill" in seq_trigger.trigger

    def test_sequence_pattern_matching(self, video_processor):
        """Test sequence pattern matching logic"""
        # Test triple kill pattern
        trigger_names = ["you have slain an enemy", "double kill", "triple kill"]
        pattern = ["you have slain an enemy", "double kill", "triple kill"]
        assert video_processor._matches_sequence_pattern(trigger_names, pattern)

        # Test incomplete pattern
        trigger_names = ["double kill", "triple kill"]
        pattern = ["you have slain an enemy", "double kill", "triple kill"]
        assert not video_processor._matches_sequence_pattern(trigger_names, pattern)

        # Test with extra triggers (should still match)
        trigger_names = ["you have slain an enemy", "double kill", "triple kill", "godlike"]
        pattern = ["you have slain an enemy", "double kill", "triple kill"]
        assert video_processor._matches_sequence_pattern(trigger_names, pattern)

    def test_create_clip_ranges(self, video_processor):
        """Test clip range creation"""
        triggers = [
            TriggerMatch(trigger="double kill", timestamp=5.0, start_time=2.0, end_time=7.0),
            TriggerMatch(trigger="triple kill", timestamp=15.0, start_time=12.0, end_time=17.0),
        ]
        
        ranges = video_processor._create_clip_ranges(triggers)
        
        assert len(ranges) == 2
        assert ranges[0].start == 2.0
        assert ranges[0].end == 7.0
        assert ranges[0].triggers == ["double kill"]

    def test_merge_overlapping_ranges(self, video_processor):
        """Test merging of overlapping clip ranges"""
        ranges = [
            ClipRange(start=1.0, end=5.0, triggers=["double kill"]),
            ClipRange(start=4.5, end=8.0, triggers=["triple kill"]),  # Overlaps
            ClipRange(start=10.0, end=15.0, triggers=["maniac"]),     # Separate
        ]
        
        merged = video_processor._merge_overlapping_ranges(ranges)
        
        assert len(merged) == 2
        assert merged[0].start == 1.0
        assert merged[0].end == 8.0
        assert "double kill" in merged[0].triggers
        assert "triple kill" in merged[0].triggers
        assert merged[1].start == 10.0
        assert merged[1].end == 15.0

    def test_merge_close_ranges(self, video_processor):
        """Test merging of ranges within threshold"""
        ranges = [
            ClipRange(start=1.0, end=5.0, triggers=["double kill"]),
            ClipRange(start=5.5, end=8.0, triggers=["triple kill"]),  # Within 1s threshold
        ]
        
        merged = video_processor._merge_overlapping_ranges(ranges)
        
        assert len(merged) == 1
        assert merged[0].start == 1.0
        assert merged[0].end == 8.0

    @patch('services.video_processor.httpx.AsyncClient')
    async def test_transcribe_video_success(self, mock_client, video_processor):
        """Test successful video transcription"""
        # Mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "words": [
                {"word": "double", "start": 1.0, "end": 1.3},
                {"word": "kill", "start": 1.3, "end": 1.6},
            ]
        }
        
        mock_client_instance = AsyncMock()
        mock_client_instance.post.return_value = mock_response
        mock_client.return_value.__aenter__.return_value = mock_client_instance
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix=".mp4", delete=False) as tmp_file:
            tmp_file.write(b"fake video content")
            tmp_file.flush()
            
            try:
                words = await video_processor._transcribe_video(tmp_file.name)
                
                assert len(words) == 2
                assert words[0].word == "double"
                assert words[0].start == 1.0
                assert words[1].word == "kill"
                assert words[1].start == 1.3
            finally:
                os.unlink(tmp_file.name)

    @patch('services.video_processor.httpx.AsyncClient')
    async def test_transcribe_video_api_error(self, mock_client, video_processor):
        """Test transcription API error handling"""
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        
        mock_client_instance = AsyncMock()
        mock_client_instance.post.return_value = mock_response
        mock_client.return_value.__aenter__.return_value = mock_client_instance
        
        with tempfile.NamedTemporaryFile(suffix=".mp4", delete=False) as tmp_file:
            tmp_file.write(b"fake video content")
            tmp_file.flush()
            
            try:
                with pytest.raises(HuggingFaceAPIError):
                    await video_processor._transcribe_video(tmp_file.name)
            finally:
                os.unlink(tmp_file.name)

    def test_missing_hf_config(self, video_processor):
        """Test error when HF config is missing"""
        video_processor.hf_endpoint = ""
        video_processor.hf_token = ""
        
        with pytest.raises(HuggingFaceAPIError):
            # This should be an async call, but we're testing the validation
            import asyncio
            asyncio.run(video_processor._transcribe_video("fake_path.mp4"))


class TestJobManager:
    def test_create_job(self, job_manager):
        """Test job creation"""
        job_id = "test-job-123"
        job_manager.create_job(job_id)
        
        status = job_manager.get_job_status(job_id)
        assert status is not None
        assert status.progress == 0
        assert status.status == "processing"
        assert status.clips == []

    def test_update_job_progress(self, job_manager):
        """Test job progress updates"""
        job_id = "test-job-456"
        job_manager.create_job(job_id)
        job_manager.update_job_progress(job_id, 50, "processing")
        
        status = job_manager.get_job_status(job_id)
        assert status.progress == 50
        assert status.status == "processing"

    def test_complete_job(self, job_manager):
        """Test job completion"""
        job_id = "test-job-789"
        clips = ["/clips/test/clip_01.mp4", "/clips/test/clip_02.mp4"]
        
        job_manager.create_job(job_id)
        job_manager.complete_job(job_id, clips)
        
        status = job_manager.get_job_status(job_id)
        assert status.progress == 100
        assert status.status == "completed"
        assert status.clips == clips

    def test_fail_job(self, job_manager):
        """Test job failure"""
        job_id = "test-job-fail"
        error_msg = "Processing failed"
        
        job_manager.create_job(job_id)
        job_manager.fail_job(job_id, error_msg)
        
        status = job_manager.get_job_status(job_id)
        assert status.status == "failed"
        assert status.error == error_msg

    def test_nonexistent_job(self, job_manager):
        """Test getting status of non-existent job"""
        status = job_manager.get_job_status("nonexistent-job")
        assert status is None
