# Video Highlight Extractor

🎮 **Automatically extract gaming highlights from videos using AI speech recognition**

A production-ready system that uses Hugging Face's speech-to-text models to identify gaming trigger words (like "double kill", "triple kill", etc.) and automatically creates highlight clips from your gaming videos.

## 🚀 Quick Start

### Prerequisites

- <PERSON><PERSON> and <PERSON>er Compose
- Hugging Face account with access to a speech-to-text inference endpoint
- Git

### 1. <PERSON><PERSON> and Setup

```bash
git clone <your-repo-url>
cd ML3
cp .env.example .env
```

### 2. Configure Environment

Edit `.env` with your AssemblyAI API key:

```bash
ASSEMBLYAI_API_KEY=your_assemblyai_api_key_here
CORS_ORIGINS=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:8000
```

**Get your free AssemblyAI API key**: https://www.assemblyai.com/ (3 hours free per month)

### 3. Run the Application

```bash
docker compose up --build
```

The application will be available at:
- **Frontend**: http://localhost:3000
- **API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs

## 🏗️ Architecture

### Backend (FastAPI)
- **FastAPI 0.111** with Python 3.11
- **Background processing** using FastAPI's BackgroundTasks
- **FFmpeg integration** for lossless video cutting
- **AssemblyAI integration** for speech-to-text with gaming vocabulary boost
- **File-based job management** (no external dependencies)

### Frontend (Next.js 14)
- **App Router** with TypeScript
- **Tailwind CSS** for styling
- **Drag-and-drop** video upload
- **Real-time status polling** (every 3 seconds)
- **Responsive design** for mobile and desktop

### Supported Triggers

#### Multi-Kill Sequences (Smart Detection)
- **Double Kill** - Captures "first blood → double kill" OR "you have slain an enemy → double kill"
- **Triple Kill** - Captures "you have slain an enemy → double kill → triple kill"
- **Mega Kill** - Captures "you have slain an enemy → double kill → triple kill → mega kill"
- **Maniac** - Captures "you have slain an enemy → double kill → triple kill → mega kill → maniac"
- **Savage** - Captures "you have slain an enemy → double kill → triple kill → mega kill → maniac → savage"

#### Individual Achievements
- **First Blood** (2s before, 2s after)
- **Godlike** (4s before, 3s after)
- **You Have Slain an Enemy** (1s before, 1s after)
- **Killing Spree** (3s before, 2s after)
- **Legendary** (4s before, 3s after)
- **Monster Kill** (4s before, 2s after)
- **Unstoppable** (4s before, 3s after)

#### Smart Sequence Detection
The system intelligently detects multi-kill sequences and creates extended clips that capture the full progression. For example, when a "Triple Kill" is detected, it automatically includes the preceding "You have slain an enemy" and "Double Kill" announcements in a single highlight clip.

## 📁 Project Structure

```
ML3/
├── backend/                 # FastAPI application
│   ├── services/           # Business logic
│   ├── uploads/            # Uploaded videos
│   ├── static/clips/       # Generated clips
│   ├── jobs/               # Job status files
│   ├── main.py             # FastAPI app
│   ├── config.py           # Configuration
│   ├── models.py           # Pydantic models
│   └── requirements.txt    # Python dependencies
├── frontend/               # Next.js application
│   ├── app/                # App Router pages
│   ├── components/         # React components
│   └── package.json        # Node.js dependencies
├── .github/workflows/      # CI/CD pipeline
├── docker-compose.yml      # Local development
└── README.md              # This file
```

## 🔧 Development

### Backend Development

```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn main:app --reload
```

### Frontend Development

```bash
cd frontend
npm install
npm run dev
```

### Running Tests

```bash
# Backend tests
cd backend
pytest

# Frontend linting
cd frontend
npm run lint
```

## 🚀 Deployment

### Vercel (Frontend)

1. Connect your GitHub repository to Vercel
2. Set environment variables:
   - `NEXT_PUBLIC_API_URL=https://your-api-domain.com`
3. Deploy automatically on push to main

### Fly.io (Backend)

```bash
# Install Fly CLI
curl -L https://fly.io/install.sh | sh

# Login and create app
fly auth login
cd backend
fly launch

# Set environment variables
fly secrets set HF_ENDPOINT=https://your-endpoint.hf.space
fly secrets set HF_TOKEN=hf_your_token_here

# Deploy
fly deploy
```

### Render (Full Stack)

1. Create a new Web Service for the backend
2. Create a new Static Site for the frontend
3. Set environment variables in Render dashboard

## 🔍 API Documentation

### Endpoints

- `POST /upload` - Upload video file
- `GET /status/{job_id}` - Get processing status
- `GET /clips/{job_id}/{filename}` - Download clip
- `GET /health` - Health check

### Example Usage

```bash
# Upload video
curl -X POST "http://localhost:8000/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@gaming_video.mp4"

# Check status
curl "http://localhost:8000/status/{job_id}"
```

## 🛠️ Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `ASSEMBLYAI_API_KEY` | AssemblyAI API key (3 hours free/month) | Required |
| `CORS_ORIGINS` | Allowed CORS origins | `http://localhost:3000` |
| `NEXT_PUBLIC_API_URL` | API URL for frontend | `http://localhost:8000` |

### Trigger Customization

Edit `backend/config.py` to modify trigger words and time ranges:

```python
# Individual triggers
triggers: dict = {
    "your_trigger": [-seconds_before, seconds_after],
    # Example: "headshot": [-2, 1]
}

# Multi-kill sequence definitions
multi_kill_sequences: dict = {
    "your_sequence": [
        ["trigger1", "trigger2", "your_sequence"]
    ]
}
```

**Note**: The system uses exact phrase matching, so "double kill" must appear as exactly those two words in sequence in the transcription.

## 🐛 Troubleshooting

### Common Issues

1. **FFmpeg not found**: Ensure FFmpeg is installed in your Docker container
2. **HF API errors**: Check your endpoint URL and token
3. **Upload fails**: Verify file size limits and video format support
4. **CORS errors**: Check CORS_ORIGINS configuration

### Logs

```bash
# View container logs
docker compose logs api
docker compose logs frontend

# Follow logs in real-time
docker compose logs -f
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🙏 Acknowledgments

- Hugging Face for speech-to-text models
- FFmpeg for video processing
- FastAPI and Next.js communities
