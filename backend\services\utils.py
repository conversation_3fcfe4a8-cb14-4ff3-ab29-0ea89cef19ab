"""Utility functions for video processing"""

import os
import subprocess
from pathlib import Path
from typing import Optional
import logging

logger = logging.getLogger(__name__)


def get_video_duration(video_path: str) -> Optional[float]:
    """Get video duration in seconds using FFprobe"""
    try:
        cmd = [
            "ffprobe",
            "-v", "quiet",
            "-show_entries", "format=duration",
            "-of", "csv=p=0",
            video_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            return float(result.stdout.strip())
        else:
            logger.error(f"FFprobe error: {result.stderr}")
            return None
            
    except (subprocess.TimeoutExpired, ValueError, FileNotFoundError) as e:
        logger.error(f"Error getting video duration: {e}")
        return None


def validate_video_file(file_path: str) -> bool:
    """Validate that the file is a proper video file"""
    if not os.path.exists(file_path):
        return False
    
    # Check file size (not empty, not too large)
    file_size = os.path.getsize(file_path)
    if file_size == 0:
        return False
    
    # Check if <PERSON><PERSON><PERSON> can read the file
    duration = get_video_duration(file_path)
    return duration is not None and duration > 0


def cleanup_temp_files(file_paths: list[str]) -> None:
    """Clean up temporary files"""
    for file_path in file_paths:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"Cleaned up temporary file: {file_path}")
        except OSError as e:
            logger.warning(f"Failed to clean up file {file_path}: {e}")


def ensure_directory_exists(directory: str) -> None:
    """Ensure a directory exists, create if it doesn't"""
    Path(directory).mkdir(parents=True, exist_ok=True)


def format_time_range(start: float, end: float) -> str:
    """Format time range for display"""
    def format_seconds(seconds: float) -> str:
        minutes = int(seconds // 60)
        secs = int(seconds % 60)
        return f"{minutes:02d}:{secs:02d}"
    
    return f"{format_seconds(start)} - {format_seconds(end)}"


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe file system usage"""
    # Remove or replace unsafe characters
    unsafe_chars = '<>:"/\\|?*'
    for char in unsafe_chars:
        filename = filename.replace(char, '_')
    
    # Limit length
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext
    
    return filename
