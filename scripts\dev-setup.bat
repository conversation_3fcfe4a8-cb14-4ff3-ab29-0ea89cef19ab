@echo off
REM Video Highlight Extractor - Development Setup Script for Windows

echo 🎮 Video Highlight Extractor - Development Setup
echo ================================================

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed. Please install Docker Desktop first.
    pause
    exit /b 1
)

REM Check if Docker Compose is available
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    docker compose version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ Docker Compose is not available. Please install Docker Compose.
        pause
        exit /b 1
    )
)

REM Create .env file if it doesn't exist
if not exist .env (
    echo 📝 Creating .env file from template...
    copy .env.example .env
    echo ⚠️  Please edit .env file with your Hugging Face credentials:
    echo    - HF_ENDPOINT: Your Hugging Face inference endpoint URL
    echo    - HF_TOKEN: Your Hugging Face API token
    echo.
    pause
)

REM Create necessary directories
echo 📁 Creating necessary directories...
if not exist backend\uploads mkdir backend\uploads
if not exist backend\static\clips mkdir backend\static\clips
if not exist backend\jobs mkdir backend\jobs

REM Build and start services
echo 🐳 Building and starting Docker services...
docker-compose up --build -d

REM Wait for services to be ready
echo ⏳ Waiting for services to start...
timeout /t 10 /nobreak >nul

REM Check if services are running
echo 🔍 Checking service health...

REM Check API health
curl -f http://localhost:8000/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ API service is running at http://localhost:8000
    echo 📚 API documentation available at http://localhost:8000/docs
) else (
    echo ❌ API service is not responding
    echo 📋 Check logs with: docker-compose logs api
)

REM Check frontend
curl -f http://localhost:3000 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Frontend service is running at http://localhost:3000
) else (
    echo ❌ Frontend service is not responding
    echo 📋 Check logs with: docker-compose logs frontend
)

echo.
echo 🎉 Setup complete!
echo.
echo 📖 Quick commands:
echo    View logs:     docker-compose logs -f
echo    Stop services: docker-compose down
echo    Restart:       docker-compose restart
echo    Rebuild:       docker-compose up --build
echo.
echo 🌐 Access the application:
echo    Frontend: http://localhost:3000
echo    API:      http://localhost:8000
echo    API Docs: http://localhost:8000/docs

pause
