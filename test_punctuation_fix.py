#!/usr/bin/env python3
"""
Test the punctuation fix with the actual transcript data
"""

import sys
sys.path.append('backend')

from backend.services.video_processor import VideoProcessor
from backend.models import WordTimestamp

def test_punctuation_fix():
    """Test trigger detection with punctuation in words"""
    print("🔧 Testing Punctuation Fix")
    print("=" * 30)
    
    # Create test words with punctuation (from actual transcript)
    test_words = [
        # "mega kill." from transcript
        WordTimestamp(word="mega", start=191.64, end=191.96),
        WordTimestamp(word="kill.", start=191.96, end=192.28),
        
        # "triple kill." from transcript  
        WordTimestamp(word="triple", start=551.13, end=551.49),
        WordTimestamp(word="kill.", start=551.49, end=551.77),
        
        # "maniac." from transcript
        WordTimestamp(word="maniac.", start=428.72, end=429.28),
        
        # "legendary" from transcript (no punctuation)
        WordTimestamp(word="legendary", start=624.07, end=624.95),
        
        # Add some context words
        WordTimestamp(word="prepare", start=620.47, end=621.23),
        WordTimestamp(word="to", start=621.23, end=621.51),
        WordTimestamp(word="die.", start=621.51, end=621.99),
    ]
    
    print("📝 Test words with punctuation:")
    for word in test_words:
        print(f"   '{word.word}' ({word.start:.2f}s - {word.end:.2f}s)")
    
    # Test trigger detection
    processor = VideoProcessor()
    triggers = processor._find_triggers(test_words)
    
    print(f"\n🎯 Trigger Detection Results:")
    print(f"   Total triggers found: {len(triggers)}")
    
    expected_triggers = ["mega kill", "triple kill", "maniac", "legendary"]
    found_trigger_names = [t.trigger for t in triggers]
    
    for trigger in triggers:
        print(f"   ✅ {trigger.trigger} at {trigger.timestamp:.2f}s")
    
    print(f"\n📊 Analysis:")
    print(f"   Expected: {expected_triggers}")
    print(f"   Found: {found_trigger_names}")
    
    missing = [t for t in expected_triggers if t not in found_trigger_names]
    if missing:
        print(f"   ❌ Missing: {missing}")
    else:
        print(f"   ✅ All expected triggers found!")
    
    return len(triggers), missing

def test_with_full_transcript_sample():
    """Test with a sample from the full transcript"""
    print("\n🎮 Testing with Full Transcript Sample")
    print("=" * 40)
    
    # Key gaming moments from the actual transcript
    gaming_words = [
        # "burst blood" 
        WordTimestamp(word="burst", start=106.66, end=107.18),
        WordTimestamp(word="blood", start=107.18, end=107.70),
        
        # "mega kill."
        WordTimestamp(word="mega", start=191.64, end=191.96),
        WordTimestamp(word="kill.", start=191.96, end=192.28),
        
        # "has been slain"
        WordTimestamp(word="has", start=324.46, end=324.74),
        WordTimestamp(word="been", start=324.74, end=324.98),
        WordTimestamp(word="slain.", start=324.98, end=325.58),
        
        # "maniac."
        WordTimestamp(word="maniac.", start=428.72, end=429.28),
        
        # "triple kill."
        WordTimestamp(word="triple", start=551.13, end=551.49),
        WordTimestamp(word="kill.", start=551.49, end=551.77),
        
        # "legendary"
        WordTimestamp(word="legendary", start=624.07, end=624.95),
    ]
    
    processor = VideoProcessor()
    triggers = processor._find_triggers(gaming_words)
    
    print(f"🎯 Results from transcript sample:")
    print(f"   Triggers found: {len(triggers)}")
    
    for trigger in triggers:
        print(f"   • {trigger.trigger} at {trigger.timestamp:.2f}s")
    
    return len(triggers)

def main():
    """Run all tests"""
    print("🚀 Punctuation Fix Test")
    print("=" * 25)
    
    # Test 1: Punctuation fix
    trigger_count, missing = test_punctuation_fix()
    
    # Test 2: Full transcript sample
    sample_count = test_with_full_transcript_sample()
    
    print(f"\n🎉 Test Results:")
    print(f"   Punctuation test: {trigger_count} triggers")
    print(f"   Transcript sample: {sample_count} triggers")
    
    if len(missing) == 0 and trigger_count >= 4:
        print(f"\n✅ SUCCESS: Punctuation fix is working!")
        print(f"   The app should now detect multiple triggers from the video.")
        print(f"   Upload the same video again to test the fix.")
    else:
        print(f"\n❌ ISSUE: Still missing triggers: {missing}")

if __name__ == "__main__":
    main()
