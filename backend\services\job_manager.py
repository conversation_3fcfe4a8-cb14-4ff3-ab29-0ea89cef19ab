import json
import os
from pathlib import Path
from typing import Optional, Dict, Any
from models import JobStatus
from config import settings


class JobManager:
    def __init__(self):
        self.jobs_dir = Path(settings.jobs_dir)
        self.jobs_dir.mkdir(exist_ok=True)
    
    def create_job(self, job_id: str) -> None:
        """Create a new job with initial status"""
        job_status = JobStatus(
            progress=0,
            clips=[],
            status="processing"
        )
        self._save_job_status(job_id, job_status)
    
    def update_job_progress(self, job_id: str, progress: int, status: str = "processing") -> None:
        """Update job progress"""
        current_status = self.get_job_status(job_id)
        if current_status:
            current_status.progress = progress
            current_status.status = status
            self._save_job_status(job_id, current_status)
    
    def add_clip(self, job_id: str, clip_path: str) -> None:
        """Add a clip to the job"""
        current_status = self.get_job_status(job_id)
        if current_status:
            current_status.clips.append(clip_path)
            self._save_job_status(job_id, current_status)
    
    def complete_job(self, job_id: str, clips: list = None) -> None:
        """Mark job as completed"""
        current_status = self.get_job_status(job_id)
        if current_status:
            current_status.progress = 100
            current_status.status = "completed"
            if clips:
                current_status.clips = clips
            self._save_job_status(job_id, current_status)
    
    def fail_job(self, job_id: str, error: str) -> None:
        """Mark job as failed"""
        current_status = self.get_job_status(job_id)
        if current_status:
            current_status.status = "failed"
            current_status.error = error
            self._save_job_status(job_id, current_status)
    
    def get_job_status(self, job_id: str) -> Optional[JobStatus]:
        """Get current job status"""
        job_file = self.jobs_dir / f"{job_id}.json"
        if not job_file.exists():
            return None
        
        try:
            with open(job_file, 'r') as f:
                data = json.load(f)
                return JobStatus(**data)
        except Exception:
            return None
    
    def _save_job_status(self, job_id: str, status: JobStatus) -> None:
        """Save job status to file"""
        job_file = self.jobs_dir / f"{job_id}.json"
        try:
            with open(job_file, 'w') as f:
                json.dump(status.model_dump(), f, indent=2)
        except Exception as e:
            print(f"Failed to save job status: {e}")
