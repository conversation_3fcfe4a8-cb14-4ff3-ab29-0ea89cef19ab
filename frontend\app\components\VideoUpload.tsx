'use client';

import { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import axios from 'axios';
import { VideoUploadState, UploadResponse } from '../types';

interface VideoUploadProps {
  onUploadComplete: (jobId: string) => void;
}

export default function VideoUpload({ onUploadComplete }: VideoUploadProps) {
  const [state, setState] = useState<VideoUploadState>({
    file: null,
    uploading: false,
    uploadProgress: 0,
    jobId: null,
    jobStatus: null,
    error: null,
  });

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      setState(prev => ({ ...prev, file, error: null }));
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'video/*': ['.mp4', '.avi', '.mov', '.mkv', '.webm']
    },
    multiple: false,
    maxSize: 500 * 1024 * 1024, // 500MB
  });

  const uploadVideo = async () => {
    if (!state.file) return;

    setState(prev => ({ ...prev, uploading: true, uploadProgress: 0, error: null }));

    const formData = new FormData();
    formData.append('file', state.file);

    try {
      const response = await axios.post<UploadResponse>(
        `${process.env.NEXT_PUBLIC_API_URL}/upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          onUploadProgress: (progressEvent) => {
            if (progressEvent.total) {
              const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              setState(prev => ({ ...prev, uploadProgress: progress }));
            }
          },
        }
      );

      setState(prev => ({
        ...prev,
        uploading: false,
        jobId: response.data.job_id,
      }));

      onUploadComplete(response.data.job_id);
    } catch (error) {
      console.error('Upload failed:', error);
      setState(prev => ({
        ...prev,
        uploading: false,
        error: 'Upload failed. Please try again.',
      }));
    }
  };

  const resetUpload = () => {
    setState({
      file: null,
      uploading: false,
      uploadProgress: 0,
      jobId: null,
      jobStatus: null,
      error: null,
    });
  };

  return (
    <div className="w-full max-w-2xl mx-auto">
      {!state.file && !state.jobId && (
        <div
          {...getRootProps()}
          className={`upload-zone ${isDragActive ? 'drag-active' : ''}`}
        >
          <input {...getInputProps()} />
          <div className="space-y-4">
            <div className="text-6xl">🎮</div>
            <div>
              <p className="text-xl font-semibold text-gray-700">
                {isDragActive ? 'Drop your Mobile Legends match here' : 'Upload your Mobile Legends match recording'}
              </p>
              <p className="text-gray-500 mt-2">
                Drag & drop or click to browse files
              </p>
            </div>
            <div className="text-sm text-gray-400">
              Supports MP4, AVI, MOV, MKV, WebM (max 500MB)
            </div>
          </div>
        </div>
      )}

      {state.file && !state.jobId && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                Ready to Upload
              </h3>
              <p className="text-gray-600">{state.file.name}</p>
              <p className="text-sm text-gray-500">
                {(state.file.size / (1024 * 1024)).toFixed(1)} MB
              </p>
            </div>
            <button
              onClick={resetUpload}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          {state.uploading && (
            <div className="mb-4">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>Uploading...</span>
                <span>{state.uploadProgress}%</span>
              </div>
              <div className="progress-bar">
                <div
                  className="progress-fill"
                  style={{ width: `${state.uploadProgress}%` }}
                />
              </div>
            </div>
          )}

          {state.error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-700">{state.error}</p>
            </div>
          )}

          <button
            onClick={uploadVideo}
            disabled={state.uploading}
            className="w-full bg-primary-600 text-white py-3 px-4 rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {state.uploading ? 'Uploading...' : 'Upload & Process Video'}
          </button>
        </div>
      )}
    </div>
  );
}
