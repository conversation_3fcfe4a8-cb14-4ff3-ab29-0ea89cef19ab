from typing import List, Optional
from pydantic import BaseModel


class JobStatus(BaseModel):
    progress: int  # 0-100
    clips: List[str] = []
    status: str = "processing"  # processing, completed, failed
    error: Optional[str] = None


class UploadResponse(BaseModel):
    job_id: str
    message: str


class WordTimestamp(BaseModel):
    word: str
    start: float
    end: float


class TriggerMatch(BaseModel):
    trigger: str
    timestamp: float
    start_time: float
    end_time: float


class ClipRange(BaseModel):
    start: float
    end: float
    triggers: List[str]
