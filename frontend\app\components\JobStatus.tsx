'use client';

import { useEffect, useState } from 'react';
import axios from 'axios';
import { JobStatus as JobStatusType } from '../types';

interface JobStatusProps {
  jobId: string;
  onReset: () => void;
}

export default function JobStatus({ jobId, onReset }: JobStatusProps) {
  const [status, setStatus] = useState<JobStatusType | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStatus = async () => {
      try {
        const response = await axios.get<JobStatusType>(
          `${process.env.NEXT_PUBLIC_API_URL}/status/${jobId}`
        );
        setStatus(response.data);
        setError(null);
      } catch (err) {
        console.error('Failed to fetch status:', err);
        setError('Failed to fetch job status');
      }
    };

    // Initial fetch
    fetchStatus();

    // Poll every 3 seconds if still processing
    const interval = setInterval(() => {
      if (status?.status === 'processing') {
        fetchStatus();
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [jobId, status?.status]);

  if (error) {
    return (
      <div className="w-full max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6">
        <div className="text-center">
          <div className="text-red-500 text-4xl mb-4">⚠️</div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Error</h3>
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={onReset}
            className="bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!status) {
    return (
      <div className="w-full max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6">
        <div className="text-center">
          <div className="animate-spin text-4xl mb-4">⏳</div>
          <p className="text-gray-600">Loading job status...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* Status Card */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">
            Processing Status
          </h3>
          <button
            onClick={onReset}
            className="text-gray-400 hover:text-gray-600 text-sm"
          >
            Upload New Video
          </button>
        </div>

        {status.status === 'processing' && (
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="animate-spin text-2xl">🔄</div>
              <div>
                <p className="font-medium text-gray-900">Processing your video...</p>
                <p className="text-sm text-gray-600">
                  Extracting speech and finding highlights
                </p>
              </div>
            </div>
            <div className="progress-bar">
              <div
                className="progress-fill"
                style={{ width: `${status.progress}%` }}
              />
            </div>
            <div className="text-sm text-gray-600 text-center">
              {status.progress}% complete
            </div>
          </div>
        )}

        {status.status === 'completed' && (
          <div className="text-center">
            <div className="text-green-500 text-4xl mb-4">✅</div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">
              Processing Complete!
            </h4>
            <p className="text-gray-600">
              Found {status.clips.length} highlight{status.clips.length !== 1 ? 's' : ''}
            </p>
          </div>
        )}

        {status.status === 'failed' && (
          <div className="text-center">
            <div className="text-red-500 text-4xl mb-4">❌</div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">
              Processing Failed
            </h4>
            <p className="text-red-600">{status.error || 'Unknown error occurred'}</p>
          </div>
        )}
      </div>

      {/* Clips Grid */}
      {status.clips.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Highlight Clips ({status.clips.length})
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {status.clips.map((clipPath, index) => (
              <div
                key={index}
                className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
              >
                <video
                  controls
                  className="w-full h-48 object-cover"
                  preload="metadata"
                >
                  <source
                    src={`${process.env.NEXT_PUBLIC_API_URL}${clipPath}`}
                    type="video/mp4"
                  />
                  Your browser does not support the video tag.
                </video>
                <div className="p-3">
                  <p className="text-sm font-medium text-gray-900">
                    Clip {index + 1}
                  </p>
                  <a
                    href={`${process.env.NEXT_PUBLIC_API_URL}${clipPath}`}
                    download={`highlight_clip_${index + 1}.mp4`}
                    className="text-sm text-primary-600 hover:text-primary-700 mt-1 inline-block"
                  >
                    Download
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
