import asyncio
import json
import subprocess
import httpx
from pathlib import Path
from typing import List, Dict, Any
import logging

from config import settings
from models import WordTimestamp, TriggerMatch, ClipRange
from services.job_manager import JobManager
from services.exceptions import TranscriptionError, FFmpegError, HuggingFaceAPIError
from services.utils import get_video_duration, validate_video_file, cleanup_temp_files

logger = logging.getLogger(__name__)


class VideoProcessor:
    def __init__(self):
        self.hf_endpoint = settings.hf_endpoint
        self.hf_token = settings.hf_token
        self.triggers = settings.triggers
        self.multi_kill_sequences = settings.multi_kill_sequences
        self.merge_gap_threshold = settings.merge_gap_threshold
    
    async def process_video(self, job_id: str, video_path: str, job_manager: JobManager):
        """Main video processing pipeline"""
        try:
            # Step 0: Validate video file
            job_manager.update_job_progress(job_id, 5, "processing")
            if not validate_video_file(video_path):
                raise ValueError("Invalid or corrupted video file")

            # Step 1: Send to HF for speech-to-text (30% progress)
            job_manager.update_job_progress(job_id, 10, "processing")
            words = await self._transcribe_video(video_path)
            job_manager.update_job_progress(job_id, 30, "processing")

            # Step 2: Find trigger words (50% progress)
            triggers = self._find_triggers(words)
            if not triggers:
                logger.info(f"No triggers found in video {job_id}")
                job_manager.complete_job(job_id, [])
                return

            job_manager.update_job_progress(job_id, 50, "processing")

            # Step 3: Create clip ranges and merge overlaps (70% progress)
            clip_ranges = self._create_clip_ranges(triggers)
            merged_ranges = self._merge_overlapping_ranges(clip_ranges)
            job_manager.update_job_progress(job_id, 70, "processing")

            # Step 4: Cut video clips (80-95% progress)
            clip_paths = await self._cut_video_clips(job_id, video_path, merged_ranges, job_manager)

            # Step 5: Complete job (100% progress)
            job_manager.complete_job(job_id, clip_paths)

        except (TranscriptionError, FFmpegError, HuggingFaceAPIError) as e:
            logger.error(f"Processing error for video {job_id}: {str(e)}")
            job_manager.fail_job(job_id, str(e))
        except Exception as e:
            logger.error(f"Unexpected error processing video {job_id}: {str(e)}")
            job_manager.fail_job(job_id, f"Unexpected error: {str(e)}")
        finally:
            # Cleanup uploaded file after processing
            cleanup_temp_files([video_path])
    
    async def _transcribe_video(self, video_path: str) -> List[WordTimestamp]:
        """Send video to Hugging Face endpoint for transcription"""
        if not self.hf_endpoint or not self.hf_token:
            raise HuggingFaceAPIError("HF_ENDPOINT and HF_TOKEN must be configured")

        headers = {
            "Authorization": f"Bearer {self.hf_token}",
        }

        try:
            async with httpx.AsyncClient(timeout=300.0) as client:
                with open(video_path, "rb") as f:
                    files = {"file": f}
                    response = await client.post(
                        self.hf_endpoint,
                        headers=headers,
                        files=files
                    )

                    if response.status_code != 200:
                        error_msg = f"HF API returned {response.status_code}: {response.text}"
                        raise HuggingFaceAPIError(error_msg)

                    # Parse response - assuming it returns word-level timestamps
                    data = response.json()
                    words = []

                    # Handle different response formats from HF
                    if "words" in data:
                        for word_data in data["words"]:
                            words.append(WordTimestamp(
                                word=word_data.get("word", "").lower(),
                                start=float(word_data.get("start", 0)),
                                end=float(word_data.get("end", 0))
                            ))
                    elif "text" in data and "chunks" in data:
                        # Alternative format with chunks
                        for chunk in data["chunks"]:
                            words.append(WordTimestamp(
                                word=chunk.get("text", "").lower(),
                                start=float(chunk.get("timestamp", [0, 0])[0]),
                                end=float(chunk.get("timestamp", [0, 0])[1])
                            ))
                    elif "text" in data:
                        # Simple text response - create single word entry
                        words.append(WordTimestamp(
                            word=data["text"].lower(),
                            start=0.0,
                            end=get_video_duration(video_path) or 0.0
                        ))
                    else:
                        raise TranscriptionError(f"Unexpected response format from HF API: {data}")

                    if not words:
                        raise TranscriptionError("No transcription data received from HF API")

                    return words

        except httpx.TimeoutException:
            raise HuggingFaceAPIError("Timeout while calling Hugging Face API")
        except httpx.RequestError as e:
            raise HuggingFaceAPIError(f"Network error calling Hugging Face API: {str(e)}")
        except Exception as e:
            if isinstance(e, (HuggingFaceAPIError, TranscriptionError)):
                raise
            raise TranscriptionError(f"Unexpected error during transcription: {str(e)}")
    
    def _find_triggers(self, words: List[WordTimestamp]) -> List[TriggerMatch]:
        """Find trigger words in the transcription with exact phrase matching"""
        # Create a continuous text from words with timestamps
        full_text = " ".join([word.word for word in words])
        logger.info(f"Full transcription text: {full_text}")

        # Find all individual trigger occurrences first
        individual_triggers = []

        for trigger_phrase, time_range in self.triggers.items():
            matches = self._find_exact_phrase_matches(trigger_phrase, words)
            for match_info in matches:
                individual_triggers.append(TriggerMatch(
                    trigger=trigger_phrase,
                    timestamp=match_info['timestamp'],
                    start_time=max(0, match_info['timestamp'] + time_range[0]),
                    end_time=match_info['timestamp'] + time_range[1]
                ))

        # Sort triggers by timestamp
        individual_triggers.sort(key=lambda x: x.timestamp)

        # Now detect multi-kill sequences and create extended clips
        sequence_triggers = self._detect_multi_kill_sequences(individual_triggers)

        # Combine individual and sequence triggers, removing duplicates
        all_triggers = self._merge_trigger_lists(individual_triggers, sequence_triggers)

        logger.info(f"Found {len(all_triggers)} total triggers: {[t.trigger for t in all_triggers]}")
        return all_triggers

    def _find_exact_phrase_matches(self, trigger_phrase: str, words: List[WordTimestamp]) -> List[dict]:
        """Find exact phrase matches in the word list"""
        matches = []
        phrase_words = trigger_phrase.lower().split()

        for i in range(len(words) - len(phrase_words) + 1):
            # Check if consecutive words exactly match the trigger phrase
            match = True
            matched_words = []

            for j, phrase_word in enumerate(phrase_words):
                word_text = words[i + j].word.lower().strip()
                # Exact match required
                if word_text != phrase_word:
                    match = False
                    break
                matched_words.append(words[i + j])

            if match and matched_words:
                # Calculate the timestamp for the trigger (middle of the phrase)
                trigger_start = matched_words[0].start
                trigger_end = matched_words[-1].end
                trigger_timestamp = (trigger_start + trigger_end) / 2

                matches.append({
                    'timestamp': trigger_timestamp,
                    'start_word_index': i,
                    'end_word_index': i + len(phrase_words) - 1,
                    'matched_text': ' '.join([w.word for w in matched_words])
                })

                logger.info(f"Found exact match for '{trigger_phrase}' at {trigger_timestamp:.2f}s: '{matches[-1]['matched_text']}'")

        return matches

    def _detect_multi_kill_sequences(self, triggers: List[TriggerMatch]) -> List[TriggerMatch]:
        """Detect multi-kill sequences and create extended clips"""
        sequence_triggers = []

        # Group triggers by time windows (within 10 seconds of each other)
        time_windows = []
        current_window = []

        for trigger in triggers:
            if not current_window or trigger.timestamp - current_window[-1].timestamp <= 10.0:
                current_window.append(trigger)
            else:
                if len(current_window) > 1:
                    time_windows.append(current_window)
                current_window = [trigger]

        if len(current_window) > 1:
            time_windows.append(current_window)

        # Analyze each time window for multi-kill sequences
        for window in time_windows:
            sequence_trigger = self._analyze_sequence_window(window)
            if sequence_trigger:
                sequence_triggers.append(sequence_trigger)

        return sequence_triggers

    def _analyze_sequence_window(self, window_triggers: List[TriggerMatch]) -> TriggerMatch:
        """Analyze a time window for multi-kill sequences"""
        trigger_names = [t.trigger for t in window_triggers]

        # Check for each multi-kill sequence pattern
        for sequence_name, patterns in self.multi_kill_sequences.items():
            for pattern in patterns:
                if self._matches_sequence_pattern(trigger_names, pattern):
                    # Create extended clip covering the entire sequence
                    start_time = min(t.timestamp for t in window_triggers)
                    end_time = max(t.timestamp for t in window_triggers)

                    # Use the time range for the highest kill in the sequence
                    time_range = self.triggers.get(sequence_name, [-5, 3])

                    sequence_trigger = TriggerMatch(
                        trigger=f"{sequence_name} sequence",
                        timestamp=(start_time + end_time) / 2,
                        start_time=max(0, start_time + time_range[0]),
                        end_time=end_time + time_range[1]
                    )

                    logger.info(f"Detected {sequence_name} sequence: {trigger_names}")
                    return sequence_trigger

        return None

    def _matches_sequence_pattern(self, trigger_names: List[str], pattern: List[str]) -> bool:
        """Check if trigger names match a sequence pattern"""
        # Convert to sets for easier comparison
        trigger_set = set(trigger_names)
        pattern_set = set(pattern)

        # All pattern triggers must be present
        return pattern_set.issubset(trigger_set)

    def _merge_trigger_lists(self, individual: List[TriggerMatch], sequences: List[TriggerMatch]) -> List[TriggerMatch]:
        """Merge individual and sequence triggers, removing overlaps"""
        all_triggers = []

        # Add sequence triggers first (they have priority)
        for seq_trigger in sequences:
            all_triggers.append(seq_trigger)

        # Add individual triggers that don't overlap with sequences
        for ind_trigger in individual:
            overlaps = False
            for seq_trigger in sequences:
                # Check if individual trigger overlaps with sequence trigger
                if (ind_trigger.timestamp >= seq_trigger.start_time and
                    ind_trigger.timestamp <= seq_trigger.end_time):
                    overlaps = True
                    break

            if not overlaps:
                all_triggers.append(ind_trigger)

        # Sort by timestamp
        all_triggers.sort(key=lambda x: x.timestamp)
        return all_triggers
    
    def _create_clip_ranges(self, triggers: List[TriggerMatch]) -> List[ClipRange]:
        """Convert triggers to clip ranges"""
        ranges = []
        for trigger in triggers:
            ranges.append(ClipRange(
                start=trigger.start_time,
                end=trigger.end_time,
                triggers=[trigger.trigger]
            ))
        return ranges
    
    def _merge_overlapping_ranges(self, ranges: List[ClipRange]) -> List[ClipRange]:
        """Merge overlapping or close clip ranges"""
        if not ranges:
            return []
        
        # Sort ranges by start time
        sorted_ranges = sorted(ranges, key=lambda x: x.start)
        merged = [sorted_ranges[0]]
        
        for current in sorted_ranges[1:]:
            last_merged = merged[-1]
            
            # Check if ranges overlap or are within merge threshold
            if current.start <= last_merged.end + self.merge_gap_threshold:
                # Merge ranges
                last_merged.end = max(last_merged.end, current.end)
                last_merged.triggers.extend(current.triggers)
            else:
                merged.append(current)
        
        return merged

    async def _cut_video_clips(self, job_id: str, video_path: str, ranges: List[ClipRange], job_manager: JobManager) -> List[str]:
        """Cut video clips using FFmpeg"""
        clip_paths = []
        clips_dir = Path(settings.clips_dir) / job_id
        clips_dir.mkdir(parents=True, exist_ok=True)

        video_duration = get_video_duration(video_path)
        if not video_duration:
            raise FFmpegError("Could not determine video duration")

        total_clips = len(ranges)
        failed_clips = 0

        for i, clip_range in enumerate(ranges):
            try:
                # Validate clip range
                if clip_range.start < 0:
                    clip_range.start = 0
                if clip_range.end > video_duration:
                    clip_range.end = video_duration
                if clip_range.start >= clip_range.end:
                    logger.warning(f"Invalid clip range {i+1}: {clip_range.start}-{clip_range.end}")
                    continue

                # Generate clip filename
                clip_filename = f"clip_{i+1:02d}.mp4"
                clip_path = clips_dir / clip_filename

                # FFmpeg command for lossless cutting
                cmd = [
                    "ffmpeg",
                    "-i", video_path,
                    "-ss", str(clip_range.start),
                    "-t", str(clip_range.end - clip_range.start),
                    "-c", "copy",  # Lossless copy
                    "-avoid_negative_ts", "make_zero",
                    "-y",  # Overwrite output file
                    str(clip_path)
                ]

                # Run FFmpeg with timeout
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )

                try:
                    _, stderr = await asyncio.wait_for(process.communicate(), timeout=60.0)
                except asyncio.TimeoutError:
                    process.kill()
                    await process.wait()
                    raise FFmpegError(f"FFmpeg timeout for clip {i+1}")

                if process.returncode == 0:
                    # Verify the output file was created and has content
                    if clip_path.exists() and clip_path.stat().st_size > 0:
                        relative_path = f"/clips/{job_id}/{clip_filename}"
                        clip_paths.append(relative_path)
                        job_manager.add_clip(job_id, relative_path)
                        logger.info(f"Successfully created clip {i+1}: {relative_path}")
                    else:
                        failed_clips += 1
                        logger.error(f"FFmpeg created empty file for clip {i+1}")
                else:
                    failed_clips += 1
                    error_msg = stderr.decode() if stderr else "Unknown FFmpeg error"
                    logger.error(f"FFmpeg error for clip {i+1}: {error_msg}")

                # Update progress (80% + 15% for clips)
                progress = 80 + int((i + 1) / total_clips * 15)
                job_manager.update_job_progress(job_id, progress, "processing")

            except Exception as e:
                failed_clips += 1
                logger.error(f"Error cutting clip {i+1}: {str(e)}")

        if failed_clips == total_clips and total_clips > 0:
            raise FFmpegError("All clip generation attempts failed")
        elif failed_clips > 0:
            logger.warning(f"{failed_clips} out of {total_clips} clips failed to generate")

        return clip_paths
