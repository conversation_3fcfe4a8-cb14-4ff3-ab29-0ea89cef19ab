import asyncio
import subprocess
from pathlib import Path
from typing import List
import logging
import tempfile
import time
import assemblyai as aai

from config import settings
from models import WordTimestamp, TriggerMatch, ClipRange
from services.job_manager import JobManager
from services.exceptions import TranscriptionError, FFmpegError, HuggingFaceAPIError
from services.utils import get_video_duration, validate_video_file, cleanup_temp_files

logger = logging.getLogger(__name__)


class VideoProcessor:
    def __init__(self):
        self.assemblyai_api_key = settings.assemblyai_api_key
        self.triggers = settings.triggers
        self.multi_kill_sequences = settings.multi_kill_sequences
        self.merge_gap_threshold = settings.merge_gap_threshold

        # Initialize AssemblyAI
        if self.assemblyai_api_key:
            aai.settings.api_key = self.assemblyai_api_key
            self.client = aai.Transcriber()
        else:
            self.client = None
            logger.warning("AssemblyAI API key not configured")
    
    async def process_video(self, job_id: str, video_path: str, job_manager: JobManager):
        """Main video processing pipeline"""
        try:
            # Step 0: Validate video file
            job_manager.update_job_progress(job_id, 5, "processing")
            if not validate_video_file(video_path):
                raise ValueError("Invalid or corrupted video file")

            # Step 1: Send to HF for speech-to-text (30% progress)
            job_manager.update_job_progress(job_id, 10, "processing")
            words = await self._transcribe_video(video_path)
            job_manager.update_job_progress(job_id, 30, "processing")

            # Step 2: Find trigger words (50% progress)
            triggers = self._find_triggers(words)
            if not triggers:
                logger.info(f"No triggers found in video {job_id}")
                job_manager.complete_job(job_id, [])
                return

            job_manager.update_job_progress(job_id, 50, "processing")

            # Step 3: Create clip ranges (70% progress)
            clip_ranges = self._create_clip_ranges(triggers)
            # Don't merge ranges - keep every trigger as a separate clip for maximum highlights
            logger.info(f"Created {len(clip_ranges)} individual clips for {len(triggers)} triggers")
            job_manager.update_job_progress(job_id, 70, "processing")

            # Step 4: Cut video clips (80-95% progress)
            clip_paths = await self._cut_video_clips(job_id, video_path, clip_ranges, job_manager)

            # Step 5: Complete job (100% progress)
            job_manager.complete_job(job_id, clip_paths)

        except (TranscriptionError, FFmpegError, HuggingFaceAPIError) as e:
            logger.error(f"Processing error for video {job_id}: {str(e)}")
            job_manager.fail_job(job_id, str(e))
        except Exception as e:
            logger.error(f"Unexpected error processing video {job_id}: {str(e)}")
            job_manager.fail_job(job_id, f"Unexpected error: {str(e)}")
        finally:
            # Cleanup uploaded file after processing
            cleanup_temp_files([video_path])
    
    async def _transcribe_video(self, video_path: str) -> List[WordTimestamp]:
        """Transcribe video using AssemblyAI with gaming vocabulary boost"""
        if not self.client:
            raise TranscriptionError("AssemblyAI API key not configured")

        try:
            # First, extract audio from video using FFmpeg
            audio_path = await self._extract_audio_from_video(video_path)

            try:
                # Configure transcription with gaming vocabulary boost
                config = aai.TranscriptionConfig(
                    word_boost=[
                        "double kill", "triple kill", "mega kill", "maniac", "savage",
                        "first blood", "godlike", "legendary", "killing spree",
                        "unstoppable", "monster kill", "you have slain an enemy"
                    ],
                    boost_param="high",  # High boost for gaming terms
                    speaker_labels=False,
                    auto_chapters=False,
                    summarization=False,
                    sentiment_analysis=False,
                    entity_detection=False,
                    iab_categories=False,
                    content_safety=False,
                    auto_highlights=False
                )

                logger.info(f"Starting AssemblyAI transcription for: {audio_path}")

                # Transcribe using AssemblyAI
                transcript = self.client.transcribe(audio_path, config=config)

                if transcript.status == aai.TranscriptStatus.error:
                    raise TranscriptionError(f"AssemblyAI transcription failed: {transcript.error}")

                words = []

                # Parse word-level timestamps from AssemblyAI response
                if transcript.words:
                    for word_data in transcript.words:
                        words.append(WordTimestamp(
                            word=word_data.text.lower().strip(),
                            start=float(word_data.start) / 1000.0,  # Convert ms to seconds
                            end=float(word_data.end) / 1000.0      # Convert ms to seconds
                        ))
                else:
                    # Fallback: if no word-level timestamps, create from text
                    logger.warning("No word-level timestamps received, using text only")
                    if transcript.text:
                        # Split text into words and estimate timestamps
                        text_words = transcript.text.lower().split()
                        duration = get_video_duration(video_path) or len(text_words)
                        time_per_word = duration / len(text_words) if text_words else 1.0

                        for i, word in enumerate(text_words):
                            start_time = i * time_per_word
                            end_time = (i + 1) * time_per_word
                            words.append(WordTimestamp(
                                word=word.strip(),
                                start=start_time,
                                end=end_time
                            ))

                if not words:
                    raise TranscriptionError("No transcription data received from AssemblyAI")

                logger.info(f"Transcribed {len(words)} words from video")
                logger.info(f"Sample text: {' '.join([w.word for w in words[:20]])}")
                logger.info(f"Full transcript: {transcript.text}")

                # Save full transcript to file for debugging
                transcript_file = Path("debug_transcript.md")
                with open(transcript_file, "w", encoding="utf-8") as f:
                    f.write(f"# Full Video Transcript Debug\n\n")
                    f.write(f"**Total Words**: {len(words)}\n\n")
                    f.write(f"**Full Text**: {transcript.text}\n\n")
                    f.write(f"## Word-by-Word Breakdown\n\n")
                    f.write(f"| # | Word | Start (s) | End (s) |\n")
                    f.write(f"|---|------|-----------|----------|\n")
                    for i, word in enumerate(words):
                        f.write(f"| {i+1} | {word.word} | {word.start:.2f} | {word.end:.2f} |\n")

                logger.info(f"💾 Full transcript saved to: {transcript_file}")

                # Debug: Log all words with timestamps for trigger analysis
                logger.info("=== FULL WORD-BY-WORD TRANSCRIPTION ===")
                for i, word in enumerate(words):
                    logger.info(f"  {i:3d}. '{word.word}' ({word.start:.2f}s - {word.end:.2f}s)")
                logger.info("=== END TRANSCRIPTION ===")

                # Debug: Check for potential gaming words
                gaming_words = ["kill", "blood", "enemy", "slain", "double", "triple", "mega", "first", "godlike", "legendary", "spree", "unstoppable", "monster", "maniac", "savage"]
                found_gaming_words = []
                for word in words:
                    if any(gaming_word in word.word.lower() for gaming_word in gaming_words):
                        found_gaming_words.append(f"'{word.word}' at {word.start:.2f}s")

                if found_gaming_words:
                    logger.info(f"🎮 Found potential gaming words: {', '.join(found_gaming_words)}")
                else:
                    logger.warning("⚠️ No gaming-related words detected in transcription!")

                return words

            finally:
                # Clean up temporary audio file
                cleanup_temp_files([audio_path])

        except Exception as e:
            if isinstance(e, TranscriptionError):
                raise
            raise TranscriptionError(f"Unexpected error during transcription: {str(e)}")

    async def _extract_audio_from_video(self, video_path: str) -> str:
        """Extract audio from video file using FFmpeg"""
        try:
            # Create temporary audio file
            temp_audio = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
            temp_audio.close()

            # FFmpeg command to extract audio
            cmd = [
                "ffmpeg",
                "-i", video_path,
                "-vn",  # No video
                "-acodec", "mp3",  # Audio codec
                "-ar", "16000",  # Sample rate (Whisper prefers 16kHz)
                "-ac", "1",  # Mono
                "-y",  # Overwrite output file
                temp_audio.name
            ]

            # Run FFmpeg
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            _, stderr = await process.communicate()

            if process.returncode != 0:
                error_msg = stderr.decode() if stderr else "Unknown FFmpeg error"
                raise FFmpegError(f"Failed to extract audio: {error_msg}")

            logger.info(f"Extracted audio to: {temp_audio.name}")
            return temp_audio.name

        except Exception as e:
            if isinstance(e, FFmpegError):
                raise
            raise FFmpegError(f"Error extracting audio: {str(e)}")
    
    def _find_triggers(self, words: List[WordTimestamp]) -> List[TriggerMatch]:
        """Find trigger words in the transcription with exact phrase matching"""
        # Create a continuous text from words with timestamps
        full_text = " ".join([word.word for word in words])
        logger.info(f"Full transcription text: {full_text}")

        # Find all individual trigger occurrences first
        individual_triggers = []

        logger.info("🔍 Searching for gaming triggers...")
        for trigger_phrase, time_range in self.triggers.items():
            logger.info(f"  Looking for: '{trigger_phrase}'")
            matches = self._find_exact_phrase_matches(trigger_phrase, words)
            logger.info(f"    Found {len(matches)} matches")
            for match_info in matches:
                individual_triggers.append(TriggerMatch(
                    trigger=trigger_phrase,
                    timestamp=match_info['timestamp'],
                    start_time=max(0, match_info['timestamp'] + time_range[0]),
                    end_time=match_info['timestamp'] + time_range[1]
                ))
                logger.info(f"    ✅ Match: '{match_info['matched_text']}' at {match_info['timestamp']:.2f}s")

        # If no exact matches found, try fuzzy matching
        if not individual_triggers:
            logger.warning("⚠️ No exact trigger matches found, trying fuzzy matching...")
            individual_triggers = self._find_fuzzy_triggers(words)

        # Sort triggers by timestamp
        individual_triggers.sort(key=lambda x: x.timestamp)
        logger.info(f"📊 Total individual triggers found: {len(individual_triggers)}")

        # Now detect multi-kill sequences and create extended clips
        sequence_triggers = self._detect_multi_kill_sequences(individual_triggers)

        # Combine individual and sequence triggers, removing duplicates
        all_triggers = self._merge_trigger_lists(individual_triggers, sequence_triggers)

        logger.info(f"Found {len(all_triggers)} total triggers: {[t.trigger for t in all_triggers]}")
        return all_triggers

    def _find_exact_phrase_matches(self, trigger_phrase: str, words: List[WordTimestamp]) -> List[dict]:
        """Find exact phrase matches in the word list"""
        matches = []
        phrase_words = trigger_phrase.lower().split()

        for i in range(len(words) - len(phrase_words) + 1):
            # Check if consecutive words exactly match the trigger phrase
            match = True
            matched_words = []

            for j, phrase_word in enumerate(phrase_words):
                word_text = words[i + j].word.lower().strip()
                # Remove punctuation for matching
                import re
                word_text_clean = re.sub(r'[^\w\s]', '', word_text)
                phrase_word_clean = re.sub(r'[^\w\s]', '', phrase_word)

                # Match with punctuation removed
                if word_text_clean != phrase_word_clean:
                    match = False
                    break
                matched_words.append(words[i + j])

            if match and matched_words:
                # Calculate the timestamp for the trigger (middle of the phrase)
                trigger_start = matched_words[0].start
                trigger_end = matched_words[-1].end
                trigger_timestamp = (trigger_start + trigger_end) / 2

                matches.append({
                    'timestamp': trigger_timestamp,
                    'start_word_index': i,
                    'end_word_index': i + len(phrase_words) - 1,
                    'matched_text': ' '.join([w.word for w in matched_words])
                })

                logger.info(f"Found exact match for '{trigger_phrase}' at {trigger_timestamp:.2f}s: '{matches[-1]['matched_text']}'")

        return matches

    def _find_fuzzy_triggers(self, words: List[WordTimestamp]) -> List[TriggerMatch]:
        """Find triggers using fuzzy matching when exact matching fails"""
        fuzzy_triggers = []

        # Define key gaming words that might appear in different forms
        fuzzy_patterns = {
            "kill": ["kill", "killed", "killing"],
            "double": ["double", "dual"],
            "triple": ["triple", "tri"],
            "blood": ["blood", "first"],
            "enemy": ["enemy", "enemies", "opponent"],
            "slain": ["slain", "slayed", "eliminated"],
            "spree": ["spree", "streak"],
            "legendary": ["legendary", "legend"],
            "godlike": ["godlike", "god", "divine"],
            "unstoppable": ["unstoppable", "unstop"],
            "maniac": ["maniac", "manic"],
            "savage": ["savage", "brutal"],
            "monster": ["monster", "mega"]
        }

        logger.info("🔍 Trying fuzzy trigger detection...")

        # Look for individual gaming words and create clips around them
        for i, word in enumerate(words):
            word_text = word.word.lower().strip()

            # Check if this word matches any gaming pattern
            for trigger_type, patterns in fuzzy_patterns.items():
                if word_text in patterns:
                    # Create a trigger for this gaming word
                    trigger_name = f"{trigger_type} (fuzzy)"
                    time_range = [-3, 3]  # Default 6-second clip

                    fuzzy_triggers.append(TriggerMatch(
                        trigger=trigger_name,
                        timestamp=word.start,
                        start_time=max(0, word.start + time_range[0]),
                        end_time=word.start + time_range[1]
                    ))

                    logger.info(f"🎯 Fuzzy match: '{word_text}' → '{trigger_name}' at {word.start:.2f}s")
                    break

        logger.info(f"📊 Fuzzy matching found {len(fuzzy_triggers)} potential triggers")
        return fuzzy_triggers

    def _detect_multi_kill_sequences(self, triggers: List[TriggerMatch]) -> List[TriggerMatch]:
        """Detect multi-kill sequences and create extended clips"""
        sequence_triggers = []

        # Group triggers by time windows (within 10 seconds of each other)
        time_windows = []
        current_window = []

        for trigger in triggers:
            if not current_window or trigger.timestamp - current_window[-1].timestamp <= 10.0:
                current_window.append(trigger)
            else:
                if len(current_window) > 1:
                    time_windows.append(current_window)
                current_window = [trigger]

        if len(current_window) > 1:
            time_windows.append(current_window)

        # Analyze each time window for multi-kill sequences
        for window in time_windows:
            sequence_trigger = self._analyze_sequence_window(window)
            if sequence_trigger:
                sequence_triggers.append(sequence_trigger)

        return sequence_triggers

    def _analyze_sequence_window(self, window_triggers: List[TriggerMatch]) -> TriggerMatch:
        """Analyze a time window for multi-kill sequences"""
        trigger_names = [t.trigger for t in window_triggers]

        # Check for each multi-kill sequence pattern
        for sequence_name, patterns in self.multi_kill_sequences.items():
            for pattern in patterns:
                if self._matches_sequence_pattern(trigger_names, pattern):
                    # Create extended clip covering the entire sequence
                    start_time = min(t.timestamp for t in window_triggers)
                    end_time = max(t.timestamp for t in window_triggers)

                    # Use the time range for the highest kill in the sequence
                    time_range = self.triggers.get(sequence_name, [-5, 3])

                    sequence_trigger = TriggerMatch(
                        trigger=f"{sequence_name} sequence",
                        timestamp=(start_time + end_time) / 2,
                        start_time=max(0, start_time + time_range[0]),
                        end_time=end_time + time_range[1]
                    )

                    logger.info(f"Detected {sequence_name} sequence: {trigger_names}")
                    return sequence_trigger

        return None

    def _matches_sequence_pattern(self, trigger_names: List[str], pattern: List[str]) -> bool:
        """Check if trigger names match a sequence pattern"""
        # Convert to sets for easier comparison
        trigger_set = set(trigger_names)
        pattern_set = set(pattern)

        # All pattern triggers must be present
        return pattern_set.issubset(trigger_set)

    def _merge_trigger_lists(self, individual: List[TriggerMatch], sequences: List[TriggerMatch]) -> List[TriggerMatch]:
        """Merge individual and sequence triggers, keeping ALL triggers for maximum clips"""
        all_triggers = []

        # Add ALL individual triggers (every gaming moment gets its own clip)
        for ind_trigger in individual:
            all_triggers.append(ind_trigger)

        # Add sequence triggers as bonus clips (longer highlights)
        for seq_trigger in sequences:
            all_triggers.append(seq_trigger)

        # Sort by timestamp
        all_triggers.sort(key=lambda x: x.timestamp)

        logger.info(f"Total triggers after merge: {len(all_triggers)}")
        for trigger in all_triggers:
            logger.info(f"  - {trigger.trigger} at {trigger.timestamp:.2f}s ({trigger.start_time:.2f}s-{trigger.end_time:.2f}s)")

        return all_triggers
    
    def _create_clip_ranges(self, triggers: List[TriggerMatch]) -> List[ClipRange]:
        """Convert triggers to clip ranges"""
        ranges = []
        for trigger in triggers:
            ranges.append(ClipRange(
                start=trigger.start_time,
                end=trigger.end_time,
                triggers=[trigger.trigger]
            ))
        return ranges
    
    def _merge_overlapping_ranges(self, ranges: List[ClipRange]) -> List[ClipRange]:
        """Merge overlapping or close clip ranges"""
        if not ranges:
            return []
        
        # Sort ranges by start time
        sorted_ranges = sorted(ranges, key=lambda x: x.start)
        merged = [sorted_ranges[0]]
        
        for current in sorted_ranges[1:]:
            last_merged = merged[-1]
            
            # Check if ranges overlap or are within merge threshold
            if current.start <= last_merged.end + self.merge_gap_threshold:
                # Merge ranges
                last_merged.end = max(last_merged.end, current.end)
                last_merged.triggers.extend(current.triggers)
            else:
                merged.append(current)
        
        return merged

    async def _cut_video_clips(self, job_id: str, video_path: str, ranges: List[ClipRange], job_manager: JobManager) -> List[str]:
        """Cut video clips using FFmpeg"""
        clip_paths = []
        clips_dir = Path(settings.clips_dir) / job_id
        clips_dir.mkdir(parents=True, exist_ok=True)

        video_duration = get_video_duration(video_path)
        if not video_duration:
            raise FFmpegError("Could not determine video duration")

        total_clips = len(ranges)
        failed_clips = 0

        for i, clip_range in enumerate(ranges):
            try:
                # Validate clip range
                if clip_range.start < 0:
                    clip_range.start = 0
                if clip_range.end > video_duration:
                    clip_range.end = video_duration
                if clip_range.start >= clip_range.end:
                    logger.warning(f"Invalid clip range {i+1}: {clip_range.start}-{clip_range.end}")
                    continue

                # Generate descriptive clip filename
                trigger_names = "_".join([t.replace(" ", "_") for t in clip_range.triggers[:2]])  # Max 2 triggers in name
                clip_filename = f"{i+1:02d}_{trigger_names}_{clip_range.start:.1f}s.mp4"
                clip_path = clips_dir / clip_filename

                # FFmpeg command for lossless cutting
                cmd = [
                    "ffmpeg",
                    "-i", video_path,
                    "-ss", str(clip_range.start),
                    "-t", str(clip_range.end - clip_range.start),
                    "-c", "copy",  # Lossless copy
                    "-avoid_negative_ts", "make_zero",
                    "-y",  # Overwrite output file
                    str(clip_path)
                ]

                # Run FFmpeg with timeout
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )

                try:
                    _, stderr = await asyncio.wait_for(process.communicate(), timeout=60.0)
                except asyncio.TimeoutError:
                    process.kill()
                    await process.wait()
                    raise FFmpegError(f"FFmpeg timeout for clip {i+1}")

                if process.returncode == 0:
                    # Verify the output file was created and has content
                    if clip_path.exists() and clip_path.stat().st_size > 0:
                        relative_path = f"/clips/{job_id}/{clip_filename}"
                        clip_paths.append(relative_path)
                        job_manager.add_clip(job_id, relative_path)
                        logger.info(f"Successfully created clip {i+1}: {relative_path}")
                    else:
                        failed_clips += 1
                        logger.error(f"FFmpeg created empty file for clip {i+1}")
                else:
                    failed_clips += 1
                    error_msg = stderr.decode() if stderr else "Unknown FFmpeg error"
                    logger.error(f"FFmpeg error for clip {i+1}: {error_msg}")

                # Update progress (80% + 15% for clips)
                progress = 80 + int((i + 1) / total_clips * 15)
                job_manager.update_job_progress(job_id, progress, "processing")

            except Exception as e:
                failed_clips += 1
                logger.error(f"Error cutting clip {i+1}: {str(e)}")

        if failed_clips == total_clips and total_clips > 0:
            raise FFmpegError("All clip generation attempts failed")
        elif failed_clips > 0:
            logger.warning(f"{failed_clips} out of {total_clips} clips failed to generate")

        return clip_paths
