import asyncio
import json
import subprocess
import httpx
from pathlib import Path
from typing import List, Dict, Any
import logging

from config import settings
from models import WordTimestamp, TriggerMatch, ClipRange
from services.job_manager import JobManager

logger = logging.getLogger(__name__)


class VideoProcessor:
    def __init__(self):
        self.hf_endpoint = settings.hf_endpoint
        self.hf_token = settings.hf_token
        self.triggers = settings.triggers
        self.merge_gap_threshold = settings.merge_gap_threshold
    
    async def process_video(self, job_id: str, video_path: str, job_manager: JobManager):
        """Main video processing pipeline"""
        try:
            # Step 1: Send to HF for speech-to-text (20% progress)
            job_manager.update_job_progress(job_id, 10, "processing")
            words = await self._transcribe_video(video_path)
            job_manager.update_job_progress(job_id, 30, "processing")
            
            # Step 2: Find trigger words (40% progress)
            triggers = self._find_triggers(words)
            job_manager.update_job_progress(job_id, 50, "processing")
            
            # Step 3: Create clip ranges and merge overlaps (60% progress)
            clip_ranges = self._create_clip_ranges(triggers)
            merged_ranges = self._merge_overlapping_ranges(clip_ranges)
            job_manager.update_job_progress(job_id, 70, "processing")
            
            # Step 4: Cut video clips (80-95% progress)
            clip_paths = await self._cut_video_clips(job_id, video_path, merged_ranges, job_manager)
            
            # Step 5: Complete job (100% progress)
            job_manager.complete_job(job_id, clip_paths)
            
        except Exception as e:
            logger.error(f"Error processing video {job_id}: {str(e)}")
            job_manager.fail_job(job_id, str(e))
    
    async def _transcribe_video(self, video_path: str) -> List[WordTimestamp]:
        """Send video to Hugging Face endpoint for transcription"""
        if not self.hf_endpoint or not self.hf_token:
            raise ValueError("HF_ENDPOINT and HF_TOKEN must be configured")
        
        headers = {
            "Authorization": f"Bearer {self.hf_token}",
        }
        
        async with httpx.AsyncClient(timeout=300.0) as client:
            with open(video_path, "rb") as f:
                files = {"file": f}
                response = await client.post(
                    self.hf_endpoint,
                    headers=headers,
                    files=files
                )
                response.raise_for_status()
                
                # Parse response - assuming it returns word-level timestamps
                data = response.json()
                words = []
                
                # Handle different response formats from HF
                if "words" in data:
                    for word_data in data["words"]:
                        words.append(WordTimestamp(
                            word=word_data.get("word", "").lower(),
                            start=float(word_data.get("start", 0)),
                            end=float(word_data.get("end", 0))
                        ))
                elif "text" in data and "chunks" in data:
                    # Alternative format with chunks
                    for chunk in data["chunks"]:
                        words.append(WordTimestamp(
                            word=chunk.get("text", "").lower(),
                            start=float(chunk.get("timestamp", [0, 0])[0]),
                            end=float(chunk.get("timestamp", [0, 0])[1])
                        ))
                
                return words
    
    def _find_triggers(self, words: List[WordTimestamp]) -> List[TriggerMatch]:
        """Find trigger words in the transcription"""
        triggers = []
        
        for trigger_phrase, time_range in self.triggers.items():
            # Look for the trigger phrase in the words
            phrase_words = trigger_phrase.lower().split()
            
            for i in range(len(words) - len(phrase_words) + 1):
                # Check if consecutive words match the trigger phrase
                match = True
                for j, phrase_word in enumerate(phrase_words):
                    if phrase_word not in words[i + j].word:
                        match = False
                        break
                
                if match:
                    # Calculate the timestamp for the trigger
                    trigger_start = words[i].start
                    trigger_end = words[i + len(phrase_words) - 1].end
                    trigger_timestamp = (trigger_start + trigger_end) / 2
                    
                    # Calculate clip start and end times
                    clip_start = max(0, trigger_timestamp + time_range[0])
                    clip_end = trigger_timestamp + time_range[1]
                    
                    triggers.append(TriggerMatch(
                        trigger=trigger_phrase,
                        timestamp=trigger_timestamp,
                        start_time=clip_start,
                        end_time=clip_end
                    ))
        
        return triggers
    
    def _create_clip_ranges(self, triggers: List[TriggerMatch]) -> List[ClipRange]:
        """Convert triggers to clip ranges"""
        ranges = []
        for trigger in triggers:
            ranges.append(ClipRange(
                start=trigger.start_time,
                end=trigger.end_time,
                triggers=[trigger.trigger]
            ))
        return ranges
    
    def _merge_overlapping_ranges(self, ranges: List[ClipRange]) -> List[ClipRange]:
        """Merge overlapping or close clip ranges"""
        if not ranges:
            return []
        
        # Sort ranges by start time
        sorted_ranges = sorted(ranges, key=lambda x: x.start)
        merged = [sorted_ranges[0]]
        
        for current in sorted_ranges[1:]:
            last_merged = merged[-1]
            
            # Check if ranges overlap or are within merge threshold
            if current.start <= last_merged.end + self.merge_gap_threshold:
                # Merge ranges
                last_merged.end = max(last_merged.end, current.end)
                last_merged.triggers.extend(current.triggers)
            else:
                merged.append(current)
        
        return merged

    async def _cut_video_clips(self, job_id: str, video_path: str, ranges: List[ClipRange], job_manager: JobManager) -> List[str]:
        """Cut video clips using FFmpeg"""
        clip_paths = []
        clips_dir = Path(settings.clips_dir) / job_id
        clips_dir.mkdir(parents=True, exist_ok=True)

        total_clips = len(ranges)
        for i, clip_range in enumerate(ranges):
            try:
                # Generate clip filename
                clip_filename = f"clip_{i+1:02d}.mp4"
                clip_path = clips_dir / clip_filename

                # FFmpeg command for lossless cutting
                cmd = [
                    "ffmpeg",
                    "-i", video_path,
                    "-ss", str(clip_range.start),
                    "-t", str(clip_range.end - clip_range.start),
                    "-c", "copy",  # Lossless copy
                    "-avoid_negative_ts", "make_zero",
                    "-y",  # Overwrite output file
                    str(clip_path)
                ]

                # Run FFmpeg
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )

                _, stderr = await process.communicate()

                if process.returncode == 0:
                    # Success - add to clip paths
                    relative_path = f"/clips/{job_id}/{clip_filename}"
                    clip_paths.append(relative_path)
                    job_manager.add_clip(job_id, relative_path)
                else:
                    logger.error(f"FFmpeg error for clip {i+1}: {stderr.decode()}")

                # Update progress (80% + 15% for clips)
                progress = 80 + int((i + 1) / total_clips * 15)
                job_manager.update_job_progress(job_id, progress, "processing")

            except Exception as e:
                logger.error(f"Error cutting clip {i+1}: {str(e)}")

        return clip_paths
