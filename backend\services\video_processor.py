import asyncio
import json
import subprocess
import httpx
from pathlib import Path
from typing import List, Dict, Any
import logging

from config import settings
from models import WordTimestamp, TriggerMatch, ClipRange
from services.job_manager import JobManager
from services.exceptions import TranscriptionError, FFmpegError, HuggingFaceAPIError
from services.utils import get_video_duration, validate_video_file, cleanup_temp_files

logger = logging.getLogger(__name__)


class VideoProcessor:
    def __init__(self):
        self.hf_endpoint = settings.hf_endpoint
        self.hf_token = settings.hf_token
        self.triggers = settings.triggers
        self.merge_gap_threshold = settings.merge_gap_threshold
    
    async def process_video(self, job_id: str, video_path: str, job_manager: JobManager):
        """Main video processing pipeline"""
        try:
            # Step 0: Validate video file
            job_manager.update_job_progress(job_id, 5, "processing")
            if not validate_video_file(video_path):
                raise ValueError("Invalid or corrupted video file")

            # Step 1: Send to HF for speech-to-text (30% progress)
            job_manager.update_job_progress(job_id, 10, "processing")
            words = await self._transcribe_video(video_path)
            job_manager.update_job_progress(job_id, 30, "processing")

            # Step 2: Find trigger words (50% progress)
            triggers = self._find_triggers(words)
            if not triggers:
                logger.info(f"No triggers found in video {job_id}")
                job_manager.complete_job(job_id, [])
                return

            job_manager.update_job_progress(job_id, 50, "processing")

            # Step 3: Create clip ranges and merge overlaps (70% progress)
            clip_ranges = self._create_clip_ranges(triggers)
            merged_ranges = self._merge_overlapping_ranges(clip_ranges)
            job_manager.update_job_progress(job_id, 70, "processing")

            # Step 4: Cut video clips (80-95% progress)
            clip_paths = await self._cut_video_clips(job_id, video_path, merged_ranges, job_manager)

            # Step 5: Complete job (100% progress)
            job_manager.complete_job(job_id, clip_paths)

        except (TranscriptionError, FFmpegError, HuggingFaceAPIError) as e:
            logger.error(f"Processing error for video {job_id}: {str(e)}")
            job_manager.fail_job(job_id, str(e))
        except Exception as e:
            logger.error(f"Unexpected error processing video {job_id}: {str(e)}")
            job_manager.fail_job(job_id, f"Unexpected error: {str(e)}")
        finally:
            # Cleanup uploaded file after processing
            cleanup_temp_files([video_path])
    
    async def _transcribe_video(self, video_path: str) -> List[WordTimestamp]:
        """Send video to Hugging Face endpoint for transcription"""
        if not self.hf_endpoint or not self.hf_token:
            raise HuggingFaceAPIError("HF_ENDPOINT and HF_TOKEN must be configured")

        headers = {
            "Authorization": f"Bearer {self.hf_token}",
        }

        try:
            async with httpx.AsyncClient(timeout=300.0) as client:
                with open(video_path, "rb") as f:
                    files = {"file": f}
                    response = await client.post(
                        self.hf_endpoint,
                        headers=headers,
                        files=files
                    )

                    if response.status_code != 200:
                        error_msg = f"HF API returned {response.status_code}: {response.text}"
                        raise HuggingFaceAPIError(error_msg)

                    # Parse response - assuming it returns word-level timestamps
                    data = response.json()
                    words = []

                    # Handle different response formats from HF
                    if "words" in data:
                        for word_data in data["words"]:
                            words.append(WordTimestamp(
                                word=word_data.get("word", "").lower(),
                                start=float(word_data.get("start", 0)),
                                end=float(word_data.get("end", 0))
                            ))
                    elif "text" in data and "chunks" in data:
                        # Alternative format with chunks
                        for chunk in data["chunks"]:
                            words.append(WordTimestamp(
                                word=chunk.get("text", "").lower(),
                                start=float(chunk.get("timestamp", [0, 0])[0]),
                                end=float(chunk.get("timestamp", [0, 0])[1])
                            ))
                    elif "text" in data:
                        # Simple text response - create single word entry
                        words.append(WordTimestamp(
                            word=data["text"].lower(),
                            start=0.0,
                            end=get_video_duration(video_path) or 0.0
                        ))
                    else:
                        raise TranscriptionError(f"Unexpected response format from HF API: {data}")

                    if not words:
                        raise TranscriptionError("No transcription data received from HF API")

                    return words

        except httpx.TimeoutException:
            raise HuggingFaceAPIError("Timeout while calling Hugging Face API")
        except httpx.RequestError as e:
            raise HuggingFaceAPIError(f"Network error calling Hugging Face API: {str(e)}")
        except Exception as e:
            if isinstance(e, (HuggingFaceAPIError, TranscriptionError)):
                raise
            raise TranscriptionError(f"Unexpected error during transcription: {str(e)}")
    
    def _find_triggers(self, words: List[WordTimestamp]) -> List[TriggerMatch]:
        """Find trigger words in the transcription"""
        triggers = []
        
        for trigger_phrase, time_range in self.triggers.items():
            # Look for the trigger phrase in the words
            phrase_words = trigger_phrase.lower().split()
            
            for i in range(len(words) - len(phrase_words) + 1):
                # Check if consecutive words match the trigger phrase
                match = True
                for j, phrase_word in enumerate(phrase_words):
                    if phrase_word not in words[i + j].word:
                        match = False
                        break
                
                if match:
                    # Calculate the timestamp for the trigger
                    trigger_start = words[i].start
                    trigger_end = words[i + len(phrase_words) - 1].end
                    trigger_timestamp = (trigger_start + trigger_end) / 2
                    
                    # Calculate clip start and end times
                    clip_start = max(0, trigger_timestamp + time_range[0])
                    clip_end = trigger_timestamp + time_range[1]
                    
                    triggers.append(TriggerMatch(
                        trigger=trigger_phrase,
                        timestamp=trigger_timestamp,
                        start_time=clip_start,
                        end_time=clip_end
                    ))
        
        return triggers
    
    def _create_clip_ranges(self, triggers: List[TriggerMatch]) -> List[ClipRange]:
        """Convert triggers to clip ranges"""
        ranges = []
        for trigger in triggers:
            ranges.append(ClipRange(
                start=trigger.start_time,
                end=trigger.end_time,
                triggers=[trigger.trigger]
            ))
        return ranges
    
    def _merge_overlapping_ranges(self, ranges: List[ClipRange]) -> List[ClipRange]:
        """Merge overlapping or close clip ranges"""
        if not ranges:
            return []
        
        # Sort ranges by start time
        sorted_ranges = sorted(ranges, key=lambda x: x.start)
        merged = [sorted_ranges[0]]
        
        for current in sorted_ranges[1:]:
            last_merged = merged[-1]
            
            # Check if ranges overlap or are within merge threshold
            if current.start <= last_merged.end + self.merge_gap_threshold:
                # Merge ranges
                last_merged.end = max(last_merged.end, current.end)
                last_merged.triggers.extend(current.triggers)
            else:
                merged.append(current)
        
        return merged

    async def _cut_video_clips(self, job_id: str, video_path: str, ranges: List[ClipRange], job_manager: JobManager) -> List[str]:
        """Cut video clips using FFmpeg"""
        clip_paths = []
        clips_dir = Path(settings.clips_dir) / job_id
        clips_dir.mkdir(parents=True, exist_ok=True)

        video_duration = get_video_duration(video_path)
        if not video_duration:
            raise FFmpegError("Could not determine video duration")

        total_clips = len(ranges)
        failed_clips = 0

        for i, clip_range in enumerate(ranges):
            try:
                # Validate clip range
                if clip_range.start < 0:
                    clip_range.start = 0
                if clip_range.end > video_duration:
                    clip_range.end = video_duration
                if clip_range.start >= clip_range.end:
                    logger.warning(f"Invalid clip range {i+1}: {clip_range.start}-{clip_range.end}")
                    continue

                # Generate clip filename
                clip_filename = f"clip_{i+1:02d}.mp4"
                clip_path = clips_dir / clip_filename

                # FFmpeg command for lossless cutting
                cmd = [
                    "ffmpeg",
                    "-i", video_path,
                    "-ss", str(clip_range.start),
                    "-t", str(clip_range.end - clip_range.start),
                    "-c", "copy",  # Lossless copy
                    "-avoid_negative_ts", "make_zero",
                    "-y",  # Overwrite output file
                    str(clip_path)
                ]

                # Run FFmpeg with timeout
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )

                try:
                    _, stderr = await asyncio.wait_for(process.communicate(), timeout=60.0)
                except asyncio.TimeoutError:
                    process.kill()
                    await process.wait()
                    raise FFmpegError(f"FFmpeg timeout for clip {i+1}")

                if process.returncode == 0:
                    # Verify the output file was created and has content
                    if clip_path.exists() and clip_path.stat().st_size > 0:
                        relative_path = f"/clips/{job_id}/{clip_filename}"
                        clip_paths.append(relative_path)
                        job_manager.add_clip(job_id, relative_path)
                        logger.info(f"Successfully created clip {i+1}: {relative_path}")
                    else:
                        failed_clips += 1
                        logger.error(f"FFmpeg created empty file for clip {i+1}")
                else:
                    failed_clips += 1
                    error_msg = stderr.decode() if stderr else "Unknown FFmpeg error"
                    logger.error(f"FFmpeg error for clip {i+1}: {error_msg}")

                # Update progress (80% + 15% for clips)
                progress = 80 + int((i + 1) / total_clips * 15)
                job_manager.update_job_progress(job_id, progress, "processing")

            except Exception as e:
                failed_clips += 1
                logger.error(f"Error cutting clip {i+1}: {str(e)}")

        if failed_clips == total_clips and total_clips > 0:
            raise FFmpegError("All clip generation attempts failed")
        elif failed_clips > 0:
            logger.warning(f"{failed_clips} out of {total_clips} clips failed to generate")

        return clip_paths
