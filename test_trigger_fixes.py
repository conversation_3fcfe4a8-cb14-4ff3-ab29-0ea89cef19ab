#!/usr/bin/env python3
"""
Test the improved trigger detection and clip generation
"""

import asyncio
import sys
import os
sys.path.append('backend')

from backend.services.video_processor import VideoProcessor
from backend.models import WordTimestamp

def create_rich_gaming_transcription():
    """Create a rich gaming transcription with multiple triggers"""
    return [
        # Opening kill
        WordTimestamp(word="first", start=5.0, end=5.3),
        WordTimestamp(word="blood", start=5.3, end=5.6),
        
        # Multi-kill sequence 1
        WordTimestamp(word="you", start=10.0, end=10.2),
        WordTimestamp(word="have", start=10.2, end=10.4),
        WordTimestamp(word="slain", start=10.4, end=10.7),
        WordTimestamp(word="an", start=10.7, end=10.8),
        WordTimestamp(word="enemy", start=10.8, end=11.1),
        
        WordTimestamp(word="double", start=12.0, end=12.3),
        WordTimestamp(word="kill", start=12.3, end=12.6),
        
        WordTimestamp(word="triple", start=14.0, end=14.3),
        WordTimestamp(word="kill", start=14.3, end=14.6),
        
        WordTimestamp(word="mega", start=16.0, end=16.3),
        WordTimestamp(word="kill", start=16.3, end=16.6),
        
        # Individual achievement
        WordTimestamp(word="godlike", start=25.0, end=25.5),
        
        # Another sequence
        WordTimestamp(word="you", start=35.0, end=35.2),
        WordTimestamp(word="have", start=35.2, end=35.4),
        WordTimestamp(word="slain", start=35.4, end=35.7),
        WordTimestamp(word="an", start=35.7, end=35.8),
        WordTimestamp(word="enemy", start=35.8, end=36.1),
        
        WordTimestamp(word="double", start=37.0, end=37.3),
        WordTimestamp(word="kill", start=37.3, end=37.6),
        
        # More individual achievements
        WordTimestamp(word="killing", start=45.0, end=45.4),
        WordTimestamp(word="spree", start=45.4, end=45.8),
        
        WordTimestamp(word="legendary", start=55.0, end=55.5),
        
        WordTimestamp(word="unstoppable", start=65.0, end=65.6),
        
        WordTimestamp(word="monster", start=75.0, end=75.4),
        WordTimestamp(word="kill", start=75.4, end=75.7),
        
        # Final sequence
        WordTimestamp(word="maniac", start=85.0, end=85.4),
        WordTimestamp(word="savage", start=87.0, end=87.4),
    ]

async def test_improved_trigger_detection():
    """Test the improved trigger detection system"""
    print("🎯 Testing Improved Trigger Detection System")
    print("=" * 50)
    
    # Create video processor
    processor = VideoProcessor()
    
    # Create rich gaming transcription
    words = create_rich_gaming_transcription()
    
    print(f"📝 Input transcription ({len(words)} words):")
    text = " ".join([w.word for w in words])
    print(f"   {text}")
    
    # Test trigger detection
    print(f"\n🔍 Detecting gaming triggers...")
    triggers = processor._find_triggers(words)
    
    print(f"\n✅ Found {len(triggers)} triggers:")
    print("-" * 60)
    
    individual_count = 0
    sequence_count = 0
    
    for i, trigger in enumerate(triggers, 1):
        duration = trigger.end_time - trigger.start_time
        trigger_type = "🔗 SEQUENCE" if "sequence" in trigger.trigger else "🎯 INDIVIDUAL"
        
        if "sequence" in trigger.trigger:
            sequence_count += 1
        else:
            individual_count += 1
        
        print(f"{i:2d}. {trigger_type} - {trigger.trigger}")
        print(f"    ⏰ Timestamp: {trigger.timestamp:.1f}s")
        print(f"    📹 Clip: {trigger.start_time:.1f}s - {trigger.end_time:.1f}s ({duration:.1f}s)")
        print()
    
    # Test clip range creation
    print(f"📊 Analysis:")
    print(f"   🎯 Individual triggers: {individual_count}")
    print(f"   🔗 Sequence triggers: {sequence_count}")
    print(f"   📹 Total clips: {len(triggers)}")
    
    # Test clip range creation
    print(f"\n📹 Testing Clip Range Creation...")
    clip_ranges = processor._create_clip_ranges(triggers)
    
    print(f"✅ Created {len(clip_ranges)} clip ranges:")
    for i, clip_range in enumerate(clip_ranges, 1):
        duration = clip_range.end - clip_range.start
        triggers_text = ", ".join(clip_range.triggers)
        print(f"   {i:2d}. {clip_range.start:.1f}s - {clip_range.end:.1f}s ({duration:.1f}s) - {triggers_text}")
    
    return triggers, clip_ranges

def test_expected_triggers():
    """Show what triggers we expect to find"""
    print("\n🎮 Expected Gaming Triggers in Test Data:")
    print("=" * 45)
    
    expected = [
        "first blood (5.0s)",
        "you have slain an enemy (10.5s)",
        "double kill (12.3s)", 
        "triple kill (14.3s)",
        "mega kill (16.3s)",
        "godlike (25.3s)",
        "you have slain an enemy (35.5s)",
        "double kill (37.3s)",
        "killing spree (45.4s)",
        "legendary (55.3s)",
        "unstoppable (65.3s)",
        "monster kill (75.2s)",
        "maniac (85.2s)",
        "savage (87.2s)"
    ]
    
    print("📋 Individual Triggers Expected:")
    for trigger in expected:
        print(f"   • {trigger}")
    
    print("\n🔗 Sequence Triggers Expected:")
    sequences = [
        "double kill sequence (10.5s → 12.3s)",
        "triple kill sequence (10.5s → 14.3s)", 
        "mega kill sequence (10.5s → 16.3s)",
        "double kill sequence (35.5s → 37.3s)"
    ]
    
    for sequence in sequences:
        print(f"   • {sequence}")
    
    print(f"\n📊 Total Expected: {len(expected)} individual + {len(sequences)} sequences = {len(expected) + len(sequences)} clips")

async def main():
    """Run all tests"""
    print("🚀 Video Highlight Extractor - Trigger Detection Test")
    print("=" * 55)
    
    # Show expected results
    test_expected_triggers()
    
    # Test improved detection
    triggers, clip_ranges = await test_improved_trigger_detection()
    
    print(f"\n🎉 TEST COMPLETE!")
    print(f"   ✅ Triggers detected: {len(triggers)}")
    print(f"   ✅ Clips to create: {len(clip_ranges)}")
    
    if len(triggers) >= 10:  # We expect at least 10+ triggers
        print(f"   🎯 SUCCESS: Detecting multiple triggers properly!")
    else:
        print(f"   ⚠️  WARNING: Expected more triggers (got {len(triggers)}, expected 10+)")
    
    print(f"\n🔧 Fixes Applied:")
    print(f"   ✅ Removed trigger merging (every trigger gets its own clip)")
    print(f"   ✅ Added sequence triggers as bonus clips")
    print(f"   ✅ Improved clip naming with trigger types")
    print(f"   ✅ Enhanced logging for debugging")
    
    print(f"\n🎮 Your app should now create many more highlight clips!")

if __name__ == "__main__":
    asyncio.run(main())
