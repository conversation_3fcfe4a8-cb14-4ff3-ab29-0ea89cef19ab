import pytest
import tempfile
import os
from fastapi.testclient import <PERSON><PERSON><PERSON>
from unittest.mock import patch, AsyncMock, MagicMock

from main import app

client = TestClient(app)


class TestIntegration:
    """Integration tests for the complete video processing workflow"""

    @patch('main.video_processor.process_video')
    def test_upload_and_status_workflow(self, mock_process):
        """Test the complete upload -> status checking workflow"""
        mock_process.return_value = None
        
        # Create a fake video file
        video_content = b"fake video content for testing"
        
        # Step 1: Upload video
        response = client.post(
            "/upload",
            files={"file": ("test_video.mp4", video_content, "video/mp4")}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "job_id" in data
        job_id = data["job_id"]
        
        # Step 2: Check initial status (should be processing)
        response = client.get(f"/status/{job_id}")
        assert response.status_code == 200
        status_data = response.json()
        assert status_data["progress"] == 0
        assert status_data["status"] == "processing"
        assert status_data["clips"] == []
        
        # Verify the background task was called
        mock_process.assert_called_once()

    def test_upload_invalid_file_type(self):
        """Test uploading non-video file"""
        text_content = b"This is not a video file"
        
        response = client.post(
            "/upload",
            files={"file": ("test.txt", text_content, "text/plain")}
        )
        
        assert response.status_code == 400
        assert "File must be a video" in response.json()["detail"]

    def test_status_nonexistent_job(self):
        """Test checking status of non-existent job"""
        response = client.get("/status/nonexistent-job-id")
        assert response.status_code == 404
        assert response.json()["detail"] == "Job not found"

    def test_health_endpoint(self):
        """Test health check endpoint"""
        response = client.get("/health")
        assert response.status_code == 200
        assert response.json()["status"] == "healthy"

    def test_root_endpoint(self):
        """Test root endpoint"""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "version" in data

    @patch('main.video_processor.process_video')
    def test_multiple_uploads(self, mock_process):
        """Test handling multiple concurrent uploads"""
        mock_process.return_value = None
        
        video_content = b"fake video content"
        job_ids = []
        
        # Upload multiple videos
        for i in range(3):
            response = client.post(
                "/upload",
                files={"file": (f"test_video_{i}.mp4", video_content, "video/mp4")}
            )
            assert response.status_code == 200
            job_ids.append(response.json()["job_id"])
        
        # Check that all jobs have unique IDs
        assert len(set(job_ids)) == 3
        
        # Check status of each job
        for job_id in job_ids:
            response = client.get(f"/status/{job_id}")
            assert response.status_code == 200
            assert response.json()["status"] == "processing"

    def test_large_file_upload(self):
        """Test uploading a large file (within limits)"""
        # Create a 1MB fake video file
        large_content = b"0" * (1024 * 1024)  # 1MB
        
        response = client.post(
            "/upload",
            files={"file": ("large_video.mp4", large_content, "video/mp4")}
        )
        
        # Should succeed (our limit is much higher)
        assert response.status_code == 200

    @patch('services.job_manager.JobManager.get_job_status')
    def test_job_status_completed_with_clips(self, mock_get_status):
        """Test status endpoint with completed job and clips"""
        mock_status = MagicMock()
        mock_status.progress = 100
        mock_status.status = "completed"
        mock_status.clips = ["/clips/test-job/clip_01.mp4", "/clips/test-job/clip_02.mp4"]
        mock_status.error = None
        mock_get_status.return_value = mock_status
        
        response = client.get("/status/test-job")
        assert response.status_code == 200
        data = response.json()
        assert data["progress"] == 100
        assert data["status"] == "completed"
        assert len(data["clips"]) == 2

    @patch('services.job_manager.JobManager.get_job_status')
    def test_job_status_failed(self, mock_get_status):
        """Test status endpoint with failed job"""
        mock_status = MagicMock()
        mock_status.progress = 50
        mock_status.status = "failed"
        mock_status.clips = []
        mock_status.error = "Processing failed due to invalid video format"
        mock_get_status.return_value = mock_status
        
        response = client.get("/status/test-job")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "failed"
        assert data["error"] == "Processing failed due to invalid video format"

    def test_cors_headers(self):
        """Test CORS headers are present"""
        response = client.options("/")
        # FastAPI automatically handles OPTIONS requests for CORS
        assert response.status_code in [200, 405]  # 405 if OPTIONS not explicitly defined

    def test_api_documentation_accessible(self):
        """Test that API documentation is accessible"""
        response = client.get("/docs")
        assert response.status_code == 200
        
        response = client.get("/openapi.json")
        assert response.status_code == 200
        data = response.json()
        assert "openapi" in data
        assert "info" in data
