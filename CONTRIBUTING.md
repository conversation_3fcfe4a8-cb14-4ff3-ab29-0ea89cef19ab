# Contributing to Video Highlight Extractor

Thank you for your interest in contributing! This document provides guidelines for contributing to the project.

## 🚀 Getting Started

### Prerequisites
- <PERSON>er and Docker Compose
- Node.js 18+ (for local frontend development)
- Python 3.11+ (for local backend development)
- Git

### Development Setup

1. **Fork and Clone**
   ```bash
   git clone https://github.com/your-username/video-highlight-extractor.git
   cd video-highlight-extractor
   ```

2. **Environment Setup**
   ```bash
   cp .env.example .env
   # Edit .env with your Hugging Face credentials
   ```

3. **Start Development Environment**
   ```bash
   # Using the setup script
   chmod +x scripts/dev-setup.sh
   ./scripts/dev-setup.sh
   
   # Or manually
   docker-compose up --build
   ```

## 🏗️ Project Structure

```
ML3/
├── backend/                 # FastAPI application
│   ├── services/           # Business logic
│   ├── main.py             # FastAPI app entry point
│   ├── config.py           # Configuration management
│   ├── models.py           # Pydantic models
│   └── test_*.py           # Test files
├── frontend/               # Next.js application
│   ├── app/                # App Router pages and components
│   └── components/         # Reusable React components
├── .github/workflows/      # CI/CD pipeline
└── scripts/               # Development scripts
```

## 🧪 Testing

### Backend Tests
```bash
cd backend
python -m pytest -v
```

### Frontend Tests
```bash
cd frontend
npm run lint
npm run type-check
```

### Integration Tests
```bash
cd backend
python -m pytest test_integration.py -v
```

## 📝 Code Style

### Backend (Python)
- Use Black for formatting: `black .`
- Follow PEP 8 guidelines
- Use type hints where possible
- Maximum line length: 127 characters

### Frontend (TypeScript)
- Use ESLint configuration provided
- Follow React best practices
- Use TypeScript strictly
- Use Tailwind CSS for styling

## 🔄 Development Workflow

1. **Create a Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes**
   - Write code following style guidelines
   - Add tests for new functionality
   - Update documentation if needed

3. **Test Your Changes**
   ```bash
   # Backend tests
   cd backend && pytest
   
   # Frontend linting
   cd frontend && npm run lint
   
   # Integration testing
   docker-compose up --build
   ```

4. **Commit Changes**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   ```

5. **Push and Create PR**
   ```bash
   git push origin feature/your-feature-name
   ```

## 📋 Pull Request Guidelines

### PR Title Format
Use conventional commits format:
- `feat:` for new features
- `fix:` for bug fixes
- `docs:` for documentation changes
- `test:` for test additions/changes
- `refactor:` for code refactoring
- `chore:` for maintenance tasks

### PR Description Template
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Code refactoring

## Testing
- [ ] Tests pass locally
- [ ] Added tests for new functionality
- [ ] Manual testing completed

## Screenshots (if applicable)
Add screenshots for UI changes

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No breaking changes (or clearly documented)
```

## 🐛 Bug Reports

Use the GitHub issue template:

```markdown
**Bug Description**
Clear description of the bug

**Steps to Reproduce**
1. Go to '...'
2. Click on '....'
3. See error

**Expected Behavior**
What should happen

**Screenshots**
If applicable

**Environment**
- OS: [e.g. Windows 10]
- Browser: [e.g. Chrome 91]
- Version: [e.g. 1.0.0]
```

## 💡 Feature Requests

```markdown
**Feature Description**
Clear description of the proposed feature

**Use Case**
Why is this feature needed?

**Proposed Solution**
How should this be implemented?

**Alternatives Considered**
Other approaches you've thought about
```

## 🏷️ Issue Labels

- `bug` - Something isn't working
- `enhancement` - New feature or request
- `documentation` - Documentation improvements
- `good first issue` - Good for newcomers
- `help wanted` - Extra attention needed
- `priority:high` - High priority
- `priority:low` - Low priority

## 🔧 Development Tips

### Backend Development
- Use `uvicorn main:app --reload` for hot reloading
- Add logging for debugging: `logger.info("Debug message")`
- Test API endpoints with `/docs` (Swagger UI)

### Frontend Development
- Use `npm run dev` for hot reloading
- Check browser console for errors
- Use React DevTools for debugging

### Docker Development
- Use `docker-compose logs -f` to follow logs
- Rebuild with `docker-compose up --build`
- Clean up with `docker-compose down -v`

## 📚 Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Next.js Documentation](https://nextjs.org/docs)
- [React Documentation](https://react.dev/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Docker Documentation](https://docs.docker.com/)

## 🤝 Community

- Be respectful and inclusive
- Help others learn and grow
- Share knowledge and best practices
- Follow the code of conduct

## 📄 License

By contributing, you agree that your contributions will be licensed under the MIT License.

## ❓ Questions?

Feel free to open an issue for questions or reach out to the maintainers.

Thank you for contributing! 🎉
