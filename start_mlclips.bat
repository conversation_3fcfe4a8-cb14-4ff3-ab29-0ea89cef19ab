@echo off
echo 🎮 MLClips - Starting Servers
echo ==============================
echo.

echo 🚀 Starting Backend API on port 8001...
start "MLClips Backend" cmd /k "cd /d %~dp0 && python -c \"import sys; sys.path.append('backend'); from main import app; import uvicorn; uvicorn.run(app, host='0.0.0.0', port=8001)\""

echo 🌐 Starting Frontend on port 3000...
timeout /t 3 /nobreak >nul
start "MLClips Frontend" cmd /k "cd /d %~dp0frontend && npm run dev"

echo.
echo ✅ MLClips is starting up...
echo ✅ Backend API: http://localhost:8001
echo ✅ Frontend App: http://localhost:3000
echo ✅ API Docs: http://localhost:8001/docs
echo.
echo 📱 Open http://localhost:3000 in your browser
echo 🎮 Your MLClips app will be ready in a few seconds!
echo.
echo ⏹️  Close the terminal windows to stop the servers
pause
