name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  lint-and-test-backend:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        cd backend
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install black flake8 pytest
    
    - name: Lint with flake8
      run: |
        cd backend
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    
    - name: Format check with black
      run: |
        cd backend
        black --check .
    
    - name: Test with pytest
      run: |
        cd backend
        pytest --verbose

  lint-and-test-frontend:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install dependencies
      run: |
        cd frontend
        npm ci
    
    - name: Lint
      run: |
        cd frontend
        npm run lint
    
    - name: Type check
      run: |
        cd frontend
        npx tsc --noEmit
    
    - name: Build
      run: |
        cd frontend
        npm run build

  build-docker-images:
    runs-on: ubuntu-latest
    needs: [lint-and-test-backend, lint-and-test-frontend]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Build backend image
      run: |
        cd backend
        docker build -t video-highlight-extractor-api:latest .
    
    - name: Build frontend image
      run: |
        cd frontend
        docker build -t video-highlight-extractor-frontend:latest .
    
    - name: Test docker-compose
      run: |
        echo "HF_ENDPOINT=https://test.example.com" > .env
        echo "HF_TOKEN=test_token" >> .env
        docker-compose config
