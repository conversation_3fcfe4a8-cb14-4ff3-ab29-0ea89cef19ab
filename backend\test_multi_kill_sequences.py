import pytest
from services.video_processor import VideoProcessor
from models import WordTimestamp


class TestMultiKillSequences:
    """Comprehensive tests for multi-kill sequence detection"""

    @pytest.fixture
    def video_processor(self):
        return VideoProcessor()

    def test_double_kill_with_first_blood(self, video_processor):
        """Test double kill sequence starting with first blood"""
        words = [
            WordTimestamp(word="first", start=1.0, end=1.3),
            WordTimestamp(word="blood", start=1.3, end=1.6),
            WordTimestamp(word="double", start=2.0, end=2.3),
            WordTimestamp(word="kill", start=2.3, end=2.6),
        ]
        
        triggers = video_processor._find_triggers(words)
        
        # Should detect either individual triggers or a sequence
        trigger_names = [t.trigger for t in triggers]
        assert "first blood" in trigger_names or "double kill sequence" in trigger_names
        assert "double kill" in trigger_names or "double kill sequence" in trigger_names

    def test_double_kill_with_slain_enemy(self, video_processor):
        """Test double kill sequence starting with 'you have slain an enemy'"""
        words = [
            WordTimestamp(word="you", start=1.0, end=1.2),
            WordTimestamp(word="have", start=1.2, end=1.4),
            WordTimestamp(word="slain", start=1.4, end=1.7),
            WordTimestamp(word="an", start=1.7, end=1.8),
            WordTimestamp(word="enemy", start=1.8, end=2.1),
            WordTimestamp(word="double", start=2.5, end=2.8),
            WordTimestamp(word="kill", start=2.8, end=3.1),
        ]
        
        triggers = video_processor._find_triggers(words)
        
        # Should detect sequence or individual triggers
        trigger_names = [t.trigger for t in triggers]
        assert len(triggers) > 0
        assert any("double kill" in name for name in trigger_names)

    def test_triple_kill_sequence(self, video_processor):
        """Test complete triple kill sequence"""
        words = [
            WordTimestamp(word="you", start=1.0, end=1.2),
            WordTimestamp(word="have", start=1.2, end=1.4),
            WordTimestamp(word="slain", start=1.4, end=1.7),
            WordTimestamp(word="an", start=1.7, end=1.8),
            WordTimestamp(word="enemy", start=1.8, end=2.1),
            WordTimestamp(word="double", start=2.5, end=2.8),
            WordTimestamp(word="kill", start=2.8, end=3.1),
            WordTimestamp(word="triple", start=3.5, end=3.8),
            WordTimestamp(word="kill", start=3.8, end=4.1),
        ]
        
        triggers = video_processor._find_triggers(words)
        
        # Should detect a sequence that includes all three elements
        sequence_triggers = [t for t in triggers if "sequence" in t.trigger]
        individual_triggers = [t for t in triggers if "sequence" not in t.trigger]
        
        # Either should have a sequence trigger or individual triggers for all parts
        if sequence_triggers:
            assert any("triple kill" in t.trigger for t in sequence_triggers)
        else:
            trigger_names = [t.trigger for t in individual_triggers]
            assert "you have slain an enemy" in trigger_names
            assert "double kill" in trigger_names
            assert "triple kill" in trigger_names

    def test_mega_kill_sequence(self, video_processor):
        """Test complete mega kill sequence"""
        words = [
            WordTimestamp(word="you", start=1.0, end=1.2),
            WordTimestamp(word="have", start=1.2, end=1.4),
            WordTimestamp(word="slain", start=1.4, end=1.7),
            WordTimestamp(word="an", start=1.7, end=1.8),
            WordTimestamp(word="enemy", start=1.8, end=2.1),
            WordTimestamp(word="double", start=2.5, end=2.8),
            WordTimestamp(word="kill", start=2.8, end=3.1),
            WordTimestamp(word="triple", start=3.5, end=3.8),
            WordTimestamp(word="kill", start=3.8, end=4.1),
            WordTimestamp(word="mega", start=4.5, end=4.8),
            WordTimestamp(word="kill", start=4.8, end=5.1),
        ]
        
        triggers = video_processor._find_triggers(words)
        
        # Should detect mega kill sequence or all individual components
        trigger_names = [t.trigger for t in triggers]
        has_mega_sequence = any("mega kill" in name and "sequence" in name for name in trigger_names)
        has_all_individual = all(phrase in trigger_names for phrase in 
                               ["you have slain an enemy", "double kill", "triple kill", "mega kill"])
        
        assert has_mega_sequence or has_all_individual

    def test_maniac_sequence(self, video_processor):
        """Test complete maniac sequence"""
        words = [
            WordTimestamp(word="you", start=1.0, end=1.2),
            WordTimestamp(word="have", start=1.2, end=1.4),
            WordTimestamp(word="slain", start=1.4, end=1.7),
            WordTimestamp(word="an", start=1.7, end=1.8),
            WordTimestamp(word="enemy", start=1.8, end=2.1),
            WordTimestamp(word="double", start=2.5, end=2.8),
            WordTimestamp(word="kill", start=2.8, end=3.1),
            WordTimestamp(word="triple", start=3.5, end=3.8),
            WordTimestamp(word="kill", start=3.8, end=4.1),
            WordTimestamp(word="mega", start=4.5, end=4.8),
            WordTimestamp(word="kill", start=4.8, end=5.1),
            WordTimestamp(word="maniac", start=5.5, end=6.0),
        ]
        
        triggers = video_processor._find_triggers(words)
        
        # Should detect maniac sequence or all components
        trigger_names = [t.trigger for t in triggers]
        assert len(triggers) > 0
        assert any("maniac" in name for name in trigger_names)

    def test_savage_sequence(self, video_processor):
        """Test complete savage sequence (highest multi-kill)"""
        words = [
            WordTimestamp(word="you", start=1.0, end=1.2),
            WordTimestamp(word="have", start=1.2, end=1.4),
            WordTimestamp(word="slain", start=1.4, end=1.7),
            WordTimestamp(word="an", start=1.7, end=1.8),
            WordTimestamp(word="enemy", start=1.8, end=2.1),
            WordTimestamp(word="double", start=2.5, end=2.8),
            WordTimestamp(word="kill", start=2.8, end=3.1),
            WordTimestamp(word="triple", start=3.5, end=3.8),
            WordTimestamp(word="kill", start=3.8, end=4.1),
            WordTimestamp(word="mega", start=4.5, end=4.8),
            WordTimestamp(word="kill", start=4.8, end=5.1),
            WordTimestamp(word="maniac", start=5.5, end=6.0),
            WordTimestamp(word="savage", start=6.5, end=7.0),
        ]
        
        triggers = video_processor._find_triggers(words)
        
        # Should detect savage sequence (the ultimate multi-kill)
        trigger_names = [t.trigger for t in triggers]
        assert len(triggers) > 0
        assert any("savage" in name for name in trigger_names)

    def test_exact_phrase_matching(self, video_processor):
        """Test that only exact phrases are matched"""
        words = [
            WordTimestamp(word="double", start=1.0, end=1.3),
            WordTimestamp(word="trouble", start=1.3, end=1.8),  # Should not match "double kill"
            WordTimestamp(word="kill", start=2.0, end=2.3),
            WordTimestamp(word="confirmed", start=2.3, end=2.8),  # Should not match "double kill"
        ]
        
        triggers = video_processor._find_triggers(words)
        
        # Should not detect "double kill" since words are not consecutive
        trigger_names = [t.trigger for t in triggers]
        assert "double kill" not in trigger_names

    def test_case_insensitive_matching(self, video_processor):
        """Test that matching is case insensitive"""
        words = [
            WordTimestamp(word="DOUBLE", start=1.0, end=1.3),
            WordTimestamp(word="KILL", start=1.3, end=1.6),
            WordTimestamp(word="First", start=2.0, end=2.3),
            WordTimestamp(word="Blood", start=2.3, end=2.6),
        ]
        
        triggers = video_processor._find_triggers(words)
        
        # Should detect triggers regardless of case
        trigger_names = [t.trigger for t in triggers]
        assert "double kill" in trigger_names or any("double kill" in name for name in trigger_names)
        assert "first blood" in trigger_names or any("first blood" in name for name in trigger_names)

    def test_individual_achievements(self, video_processor):
        """Test detection of individual achievement triggers"""
        words = [
            WordTimestamp(word="godlike", start=1.0, end=1.5),
            WordTimestamp(word="killing", start=3.0, end=3.4),
            WordTimestamp(word="spree", start=3.4, end=3.8),
            WordTimestamp(word="legendary", start=5.0, end=5.5),
            WordTimestamp(word="unstoppable", start=7.0, end=7.6),
        ]
        
        triggers = video_processor._find_triggers(words)
        
        # Should detect individual achievement triggers
        trigger_names = [t.trigger for t in triggers]
        assert "godlike" in trigger_names
        assert "killing spree" in trigger_names
        assert "legendary" in trigger_names
        assert "unstoppable" in trigger_names
