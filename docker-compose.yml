version: '3.8'

services:
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - HF_ENDPOINT=${HF_ENDPOINT}
      - HF_TOKEN=${HF_TOKEN}
      - CORS_ORIGINS=http://localhost:3000
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/static:/app/static
      - ./backend/jobs:/app/jobs
    networks:
      - app-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    depends_on:
      - api
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  uploads:
  static:
  jobs:
